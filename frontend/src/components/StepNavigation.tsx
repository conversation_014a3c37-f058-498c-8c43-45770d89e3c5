"use client";

import React from "react";
import { Steps, Typography } from "antd";
import { FileTextOutlined, SettingOutlined, LineChartOutlined } from "@ant-design/icons";

const { Text } = Typography;

type AppState = "file-selection" | "settings-configuration" | "analysis";

interface StepNavigationProps {
  currentStep: AppState;
  className?: string;
}

const StepNavigation: React.FC<StepNavigationProps> = ({ currentStep, className = "" }) => {
  const getCurrentStepIndex = () => {
    switch (currentStep) {
      case "file-selection":
        return 0;
      case "settings-configuration":
        return 1;
      case "analysis":
        return 2;
      default:
        return 0;
    }
  };

  const steps = [
    {
      title: "File Selection",
      description: "Choose EDF file",
      icon: <FileTextOutlined />,
    },
    {
      title: "Configuration",
      description: "Set parameters",
      icon: <SettingOutlined />,
    },
    {
      title: "Analysis",
      description: "View results",
      icon: <LineChartOutlined />,
    },
  ];

  return (
    <div className={`bg-white border-b border-gray-200 px-6 py-4 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="mb-3">
          <Text type="secondary" className="text-sm">
            HFO Detection Workflow
          </Text>
        </div>
        <Steps
          current={getCurrentStepIndex()}
          size="small"
          items={steps}
          className="step-navigation"
        />
      </div>
    </div>
  );
};

export default StepNavigation;
