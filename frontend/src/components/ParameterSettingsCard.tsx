"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Collapse, Button, Space, Alert, Typography, Badge, Divider, Row, Col, Tag, message } from "antd";
import {
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ArrowLeftOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { AnalysisParameters, FileInfo, ValidationErrors, DEFAULT_PARAMETERS } from "@/types/eeg";
import { validateAllParameters, areParametersValid } from "@/utils/validation";
import ThresholdSection from "./ParameterSections/ThresholdSection";
import MontageSection from "./ParameterSections/MontageSection";
import FrequencySection from "./ParameterSections/FrequencySection";
import TimeSegmentSection from "./ParameterSections/TimeSegmentSection";
import ChannelSelectionSection from "./ParameterSections/ChannelSelectionSection";

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface ParameterSettingsCardProps {
  fileInfo: FileInfo;
  onParametersChange: (parameters: AnalysisParameters) => void;
  onValidationChange: (isValid: boolean, errors: ValidationErrors) => void;
  onStartAnalysis: () => void;
  onBack: () => void;
  initialParameters?: AnalysisParameters;
}

const ParameterSettingsCard: React.FC<ParameterSettingsCardProps> = ({
  fileInfo,
  onParametersChange,
  onValidationChange,
  onStartAnalysis,
  onBack,
  initialParameters = DEFAULT_PARAMETERS,
}) => {
  const [parameters, setParameters] = useState<AnalysisParameters>(initialParameters);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [activeKeys, setActiveKeys] = useState<string[]>(["thresholds"]);

  // Initialize with enhanced defaults when fileInfo is available
  useEffect(() => {
    if (fileInfo && parameters.channel_selection.selected_leads.length === 0) {
      const enhancedDefaults = {
        ...parameters,
        channel_selection: {
          ...parameters.channel_selection,
          selected_leads: fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
        },
      };
      setParameters(enhancedDefaults);
    }
  }, [fileInfo]); // Only run when fileInfo changes

  // Validate parameters whenever they change
  useEffect(() => {
    const errors = validateAllParameters(parameters, fileInfo);
    setValidationErrors(errors);
    const isValid = areParametersValid(errors);
    onValidationChange(isValid, errors);
    onParametersChange(parameters);
  }, [parameters, fileInfo, onValidationChange, onParametersChange]);

  const updateParameters = (updates: Partial<AnalysisParameters>) => {
    setParameters((prev) => ({ ...prev, ...updates }));
  };

  const resetToDefaults = () => {
    // Create enhanced defaults with file-specific information
    const enhancedDefaults = {
      ...DEFAULT_PARAMETERS,
      channel_selection: {
        ...DEFAULT_PARAMETERS.channel_selection,
        selected_leads: fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
      },
    };
    setParameters(enhancedDefaults);
    message.success("Parameters reset to default values with file-specific settings");
  };

  const isValid = areParametersValid(validationErrors);
  const hasErrors = Object.keys(validationErrors).length > 0;

  const getSectionStatus = (sectionKey: keyof ValidationErrors) => {
    return validationErrors[sectionKey] ? "error" : "valid";
  };

  const getSectionBadge = (sectionKey: keyof ValidationErrors) => {
    const errors = validationErrors[sectionKey];
    if (errors && errors.length > 0) {
      return <Badge count={errors.length} className="ml-2" />;
    }
    return <CheckCircleOutlined className="ml-2 text-green-500" />;
  };

  const renderPanelHeader = (title: string, sectionKey: keyof ValidationErrors) => {
    const status = getSectionStatus(sectionKey);
    return (
      <Space>
        {status === "error" ? <ExclamationCircleOutlined className="text-red-500" /> : <CheckCircleOutlined className="text-green-500" />}
        <Text strong>{title}</Text>
        {validationErrors[sectionKey] && (
          <Tag color="red">
            {validationErrors[sectionKey].length} error{validationErrors[sectionKey].length > 1 ? "s" : ""}
          </Tag>
        )}
      </Space>
    );
  };

  return (
    <Card
      className="max-w-6xl mx-auto"
      styles={{
        body: { padding: "32px" },
      }}
    >
      <Space direction="vertical" size="large" className="w-full">
        {/* Header */}
        <div>
          <Space align="center" className="mb-4">
            <SettingOutlined style={{ fontSize: 24 }} />
            <Title level={2} className="!mb-0">
              Analysis Parameters Configuration
            </Title>
          </Space>
          <Text type="secondary">Configure the parameters for HFO detection analysis</Text>
        </div>

        {/* File Info */}
        <Alert
          message="File Information"
          description={
            <Row gutter={[16, 8]} className="mt-2">
              <Col span={8}>
                <Text strong>Filename:</Text> <Text code>{fileInfo.filename}</Text>
              </Col>
              <Col span={8}>
                <Text strong>Duration:</Text> <Text>{fileInfo.duration_seconds}s</Text>
              </Col>
              <Col span={8}>
                <Text strong>Channels:</Text> <Text>{fileInfo.channels.length}</Text>
              </Col>
              <Col span={8}>
                <Text strong>Sampling Rate:</Text> <Text>{fileInfo.sampling_rate} Hz</Text>
              </Col>
              <Col span={8}>
                <Text strong>Start:</Text>{" "}
                <Text>
                  {fileInfo.start_date} {fileInfo.start_time}
                </Text>
              </Col>
              <Col span={8}>
                <Text strong>Montage:</Text> <Text>-</Text>
              </Col>
            </Row>
          }
          type="info"
          showIcon
        />

        {/* Validation Status */}
        {hasErrors && (
          <Alert
            message="Validation Status"
            description={
              <div>
                <Text>Please resolve all validation errors before starting the analysis.</Text>
                <ul className="mt-2 list-disc list-inside">
                  {Object.entries(validationErrors).map(([section, errors]) =>
                    errors.map((error, index) => (
                      <li key={`${section}-${index}`} className="text-red-600">
                        <Text type="danger">{error}</Text>
                      </li>
                    ))
                  )}
                </ul>
              </div>
            }
            type="error"
            showIcon
          />
        )}

        {/* Parameter Sections in Collapse */}
        <Collapse activeKey={activeKeys} onChange={setActiveKeys} size="large" className="parameter-collapse">
          <Panel
            header={renderPanelHeader("Threshold Parameters", "thresholds")}
            key="thresholds"
            className={validationErrors.thresholds ? "error-panel" : ""}
          >
            <ThresholdSection
              parameters={parameters.thresholds}
              onUpdate={(thresholds) => updateParameters({ thresholds })}
              errors={validationErrors.thresholds || []}
              isExpanded={true}
              onToggle={() => {}}
              status={getSectionStatus("thresholds")}
            />
          </Panel>

          <Panel
            header={renderPanelHeader("Montage Configuration", "montage")}
            key="montage"
            className={validationErrors.montage ? "error-panel" : ""}
          >
            <MontageSection
              parameters={parameters.montage}
              onUpdate={(montage) => updateParameters({ montage })}
              errors={validationErrors.montage || []}
              isExpanded={true}
              onToggle={() => {}}
              status={getSectionStatus("montage")}
              availableChannels={fileInfo.channels}
            />
          </Panel>

          <Panel
            header={renderPanelHeader("Frequency Bands", "frequency")}
            key="frequency"
            className={validationErrors.frequency ? "error-panel" : ""}
          >
            <FrequencySection
              parameters={parameters.frequency}
              onUpdate={(frequency) => updateParameters({ frequency })}
              errors={validationErrors.frequency || []}
              isExpanded={true}
              onToggle={() => {}}
              status={getSectionStatus("frequency")}
              fileInfo={fileInfo}
            />
          </Panel>

          <Panel
            header={renderPanelHeader("Time Segment", "time_segment")}
            key="time_segment"
            className={validationErrors.time_segment ? "error-panel" : ""}
          >
            <TimeSegmentSection
              parameters={parameters.time_segment}
              onUpdate={(time_segment) => updateParameters({ time_segment })}
              errors={validationErrors.time_segment || []}
              isExpanded={true}
              onToggle={() => {}}
              status={getSectionStatus("time_segment")}
              fileDuration={fileInfo.duration_seconds}
            />
          </Panel>

          <Panel
            header={renderPanelHeader("Channel Selection", "channel_selection")}
            key="channel_selection"
            className={validationErrors.channel_selection ? "error-panel" : ""}
          >
            <ChannelSelectionSection
              parameters={parameters.channel_selection}
              onUpdate={(channel_selection) => updateParameters({ channel_selection })}
              errors={validationErrors.channel_selection || []}
              isExpanded={true}
              onToggle={() => {}}
              status={getSectionStatus("channel_selection")}
              availableChannels={fileInfo?.channels || []}
            />
          </Panel>
        </Collapse>

        <Divider />

        {/* Action Buttons */}
        <Row gutter={16}>
          <Col span={6}>
            <Button type="default" size="large" block icon={<ArrowLeftOutlined />} onClick={onBack}>
              Back to File Selection
            </Button>
          </Col>
          <Col span={6}>
            <Button type="default" size="large" block icon={<ReloadOutlined />} onClick={resetToDefaults}>
              Reset to Defaults
            </Button>
          </Col>
          <Col span={12}>
            <Button
              type="primary"
              size="large"
              block
              icon={<PlayCircleOutlined />}
              onClick={onStartAnalysis}
              className="!bg-black !border-black hover:!bg-gray-800 disabled:!bg-gray-300"
            >
              {isValid ? "Start Analysis" : "Start Analysis (will use defaults for invalid parameters)"}
            </Button>
          </Col>
        </Row>

        {/* Help Text */}
        <Alert
          message="Tips"
          description={
            <ul className="list-disc list-inside space-y-1">
              <li>All sections must be valid (green checkmark) before analysis can begin</li>
              <li>Click on each section to expand and configure parameters</li>
              <li>Hover over parameter labels for detailed descriptions</li>
              <li>Use &quot;Reset to Defaults&quot; to restore recommended settings</li>
            </ul>
          }
          type="info"
          showIcon
          className="mt-4"
        />
      </Space>
    </Card>
  );
};

export default ParameterSettingsCard;
