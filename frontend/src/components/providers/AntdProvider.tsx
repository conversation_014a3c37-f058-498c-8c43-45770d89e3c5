"use client";

import React from "react";
import { ConfigProvider, theme } from "antd";

interface AntdProviderProps {
  children: React.ReactNode;
}

const AntdProvider: React.FC<AntdProviderProps> = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          // Primary colors
          colorPrimary: "#000000",
          colorBgContainer: "#ffffff",
          colorBgElevated: "#ffffff",
          colorBgLayout: "#fafafa",

          // Text colors
          colorText: "#000000",
          colorTextSecondary: "#4a4a4a",
          colorTextTertiary: "#9a9a9a",
          colorTextQuaternary: "#c5c5c5",

          // Border colors
          colorBorder: "#e5e5e5",
          colorBorderSecondary: "#f0f0f0",

          // Success/Error/Warning colors
          colorSuccess: "#52c41a",
          colorWarning: "#faad14",
          colorError: "#ef4444",
          colorInfo: "#1890ff",

          // Border radius
          borderRadius: 6,
          borderRadiusLG: 8,
          borderRadiusSM: 4,

          // Font settings
          fontSize: 14,
          fontSizeHeading1: 24,
          fontSizeHeading2: 20,
          fontSizeHeading3: 18,
          fontSizeHeading4: 16,
          fontSizeHeading5: 14,

          // Motion
          motionDurationFast: "0.1s",
          motionDurationMid: "0.2s",
          motionDurationSlow: "0.3s",

          // Control height
          controlHeight: 36,
          controlHeightLG: 40,
          controlHeightSM: 32,

          // Box shadow
          boxShadow: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
          boxShadowSecondary: "0 2px 4px 0 rgb(0 0 0 / 0.06)",
        },
        algorithm: theme.defaultAlgorithm,
        components: {
          Button: {
            colorPrimary: "#000000",
            algorithm: true,
            primaryShadow: "0 2px 4px 0 rgb(0 0 0 / 0.06)",
          },
          Input: {
            activeBorderColor: "#000000",
            hoverBorderColor: "#4a4a4a",
            colorBgContainer: "#ffffff",
          },
          Select: {
            colorBgContainer: "#ffffff",
            optionSelectedBg: "#f5f5f5",
          },
          Card: {
            colorBgContainer: "#ffffff",
            colorBorderSecondary: "#e5e5e5",
            boxShadowTertiary: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
          },
          Collapse: {
            headerBg: "#fafafa",
            contentBg: "#ffffff",
            colorBorder: "#e5e5e5",
          },
          Slider: {
            trackBg: "#000000",
            trackHoverBg: "#333333",
            handleColor: "#000000",
            handleActiveColor: "#000000",
            dotBorderColor: "#e5e5e5",
            railBg: "#f0f0f0",
            railHoverBg: "#e5e5e5",
          },
          Progress: {
            defaultColor: "#000000",
          },
          Alert: {
            colorInfoBg: "#f0f9ff",
            colorInfoBorder: "#91d5ff",
            colorSuccessBg: "#f6ffed",
            colorSuccessBorder: "#b7eb8f",
            colorWarningBg: "#fffbe6",
            colorWarningBorder: "#ffe58f",
            colorErrorBg: "#fff2f0",
            colorErrorBorder: "#ffccc7",
          },
          Badge: {
            colorBgContainer: "#000000",
            colorPrimary: "#000000",
          },
          Tag: {
            defaultBg: "#fafafa",
            defaultColor: "#000000",
          },
          Divider: {
            colorSplit: "#e5e5e5",
          },
          Upload: {
            colorBorder: "#e5e5e5",
            colorPrimaryHover: "#333333",
          },
          Form: {
            labelColor: "#000000",
            labelRequiredMarkColor: "#ef4444",
          },
          Radio: {
            colorPrimary: "#000000",
            colorBorder: "#e5e5e5",
            buttonSolidCheckedBg: "#000000",
            buttonSolidCheckedHoverBg: "#333333",
          },
          Checkbox: {
            colorPrimary: "#000000",
            colorBorder: "#e5e5e5",
          },
          Switch: {
            colorPrimary: "#000000",
            colorPrimaryHover: "#333333",
          },
          Steps: {
            colorPrimary: "#000000",
            colorTextDescription: "#9a9a9a",
            colorFillContent: "#fafafa",
            colorSplit: "#e5e5e5",
          },
          Spin: {
            colorPrimary: "#000000",
          },
          Layout: {
            headerBg: "#ffffff",
            bodyBg: "#fafafa",
            triggerBg: "#f0f0f0",
          },
          Table: {
            colorBgContainer: "#ffffff",
            headerBg: "#fafafa",
            headerColor: "#000000",
            borderColor: "#e5e5e5",
            rowHoverBg: "#fafafa",
          },
          Tooltip: {
            colorBgSpotlight: "rgba(0, 0, 0, 0.85)",
            colorTextLightSolid: "#ffffff",
          },
          Transfer: {
            colorBgContainer: "#ffffff",
            colorBorder: "#e5e5e5",
            colorBgBase: "#fafafa",
          },
          DatePicker: {
            colorBgContainer: "#ffffff",
            colorBorder: "#e5e5e5",
            colorPrimary: "#000000",
            colorTextHeading: "#000000",
          },
          Tabs: {
            colorPrimary: "#000000",
            colorBorderSecondary: "#e5e5e5",
            itemHoverColor: "#4a4a4a",
            itemSelectedColor: "#000000",
          },
          Dropdown: {
            colorBgContainer: "#ffffff",
            colorPrimary: "#000000",
            colorText: "#000000",
            controlItemBgHover: "#fafafa",
            controlItemBgActive: "#f0f0f0",
          },
          Menu: {
            colorBgContainer: "#ffffff",
            colorItemBgHover: "#fafafa",
            colorItemBgSelected: "#f0f0f0",
            colorItemTextSelected: "#000000",
          },
          Notification: {
            colorBgElevated: "#ffffff",
            colorIcon: "#000000",
            colorIconHover: "#333333",
            colorText: "#000000",
            colorTextHeading: "#000000",
          },
          Message: {
            colorBgElevated: "#ffffff",
            colorText: "#000000",
            contentBg: "#ffffff",
          },
          Statistic: {
            colorTextDescription: "#9a9a9a",
            colorTextHeading: "#000000",
            contentFontSize: 24,
            titleFontSize: 14,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default AntdProvider;
