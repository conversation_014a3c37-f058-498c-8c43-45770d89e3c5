import React from 'react';
import { SectionHeader } from '../ui/SectionHeader';
import { ErrorDisplay } from '../ui/ErrorDisplay';

export interface BaseParameterSectionProps {
  title: string;
  status: 'valid' | 'error';
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

export const BaseParameterSection: React.FC<BaseParameterSectionProps> = ({
  title,
  status,
  errors,
  isExpanded,
  onToggle,
  children
}) => {
  return (
    <div className="border border-gray-200 rounded-lg">
      <SectionHeader
        title={title}
        status={status}
        errorCount={errors.length}
        isExpanded={isExpanded}
        onToggle={onToggle}
      />
      {isExpanded && (
        <div className="px-4 pb-4 border-t border-gray-100">
          {errors.length > 0 && <ErrorDisplay errors={errors} />}
          {children}
        </div>
      )}
    </div>
  );
};