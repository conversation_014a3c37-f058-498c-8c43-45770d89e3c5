export const TIME_SEGMENT_MODES = [
  {
    value: 'entire_file',
    label: 'Entire File',
    description: 'Analyze the complete EDF file from start to end'
  },
  {
    value: 'start_end_times',
    label: 'Start & End Times',
    description: 'Specify exact start and end date/time'
  },
  {
    value: 'start_time_duration',
    label: 'Start Time & Duration',
    description: 'Specify start time and duration in seconds'
  },
] as const;

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hours}h ${minutes}m ${secs}s`;
};