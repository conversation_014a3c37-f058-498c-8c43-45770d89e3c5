"use client";

import React, { useState } from "react";
import { Plus, X } from "lucide-react";
import { ChannelSelection } from "@/types/eeg";
import { BaseParameterSection } from "./BaseParameterSection";
import { InfoBox } from "@/components/ui/InfoBox";

interface ChannelSelectionSectionProps {
  parameters: ChannelSelection;
  onUpdate: (parameters: ChannelSelection) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: 'valid' | 'error';
  availableChannels: string[];
}

const ChannelSelectionSection: React.FC<ChannelSelectionSectionProps> = ({
  parameters,
  onUpdate,
  errors,
  isExpanded,
  onToggle,
  status,
  availableChannels,
}) => {
  const [newContactSpec, setNewContactSpec] = useState<{ lead: string; spec: string }>({
    lead: '',
    spec: '',
  });

  const toggleChannel = (channel: string) => {
    const isSelected = parameters.selected_leads.includes(channel);
    const newSelectedLeads = isSelected
      ? parameters.selected_leads.filter(lead => lead !== channel)
      : [...parameters.selected_leads, channel];

    onUpdate({
      ...parameters,
      selected_leads: newSelectedLeads,
    });
  };

  const selectAllChannels = () => {
    onUpdate({
      ...parameters,
      selected_leads: [...availableChannels],
    });
  };

  const clearAllChannels = () => {
    onUpdate({
      ...parameters,
      selected_leads: [],
    });
  };

  const addContactSpecification = () => {
    if (newContactSpec.lead && newContactSpec.spec) {
      onUpdate({
        ...parameters,
        contact_specifications: {
          ...parameters.contact_specifications,
          [newContactSpec.lead]: newContactSpec.spec,
        },
      });
      setNewContactSpec({ lead: '', spec: '' });
    }
  };

  const removeContactSpecification = (lead: string) => {
    const { [lead]: _, ...rest } = parameters.contact_specifications;
    onUpdate({
      ...parameters,
      contact_specifications: rest,
    });
  };

  const updateContactSpecification = (lead: string, spec: string) => {
    onUpdate({
      ...parameters,
      contact_specifications: {
        ...parameters.contact_specifications,
        [lead]: spec,
      },
    });
  };

  return (
    <BaseParameterSection
      title="Channel Selection"
      status={status}
      errors={errors}
      isExpanded={isExpanded}
      onToggle={onToggle}
    >
      <div className="mt-4 space-y-4">
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700">
                  Selected Channels ({parameters.selected_leads.length}/{availableChannels.length})
                </label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={selectAllChannels}
                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Select All
                  </button>
                  <button
                    onClick={clearAllChannels}
                    className="text-xs text-gray-600 hover:text-gray-800 font-medium"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {availableChannels.map((channel) => {
                    const isSelected = parameters.selected_leads.includes(channel);
                    return (
                      <label
                        key={channel}
                        className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-colors text-sm ${
                          isSelected
                            ? "bg-blue-50 border border-blue-200"
                            : "hover:bg-gray-50"
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleChannel(channel)}
                          className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span className={`font-mono ${
                          isSelected ? "text-blue-900 font-medium" : "text-gray-700"
                        }`}>
                          {channel}
                        </span>
                      </label>
                    );
                  })}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Contact Specifications (Optional)
              </label>
              
              {Object.entries(parameters.contact_specifications).length > 0 && (
                <div className="space-y-2 mb-3">
                  {Object.entries(parameters.contact_specifications).map(([lead, spec]) => (
                    <div key={lead} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <span className="text-sm font-mono text-gray-700 min-w-0 flex-shrink-0">
                        {lead}:
                      </span>
                      <input
                        type="text"
                        value={spec}
                        onChange={(e) => updateContactSpecification(lead, e.target.value)}
                        className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., 1-5,7,9"
                      />
                      <button
                        onClick={() => removeContactSpecification(lead)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex items-center gap-2">
                <select
                  value={newContactSpec.lead}
                  onChange={(e) => setNewContactSpec(prev => ({ ...prev, lead: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select channel...</option>
                  {parameters.selected_leads
                    .filter(lead => !parameters.contact_specifications[lead])
                    .map((lead) => (
                      <option key={lead} value={lead}>
                        {lead}
                      </option>
                    ))}
                </select>
                <input
                  type="text"
                  value={newContactSpec.spec}
                  onChange={(e) => setNewContactSpec(prev => ({ ...prev, spec: e.target.value }))}
                  placeholder="e.g., 1-5,7,9"
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={addContactSpecification}
                  disabled={!newContactSpec.lead || !newContactSpec.spec}
                  className={`p-2 rounded transition-colors ${
                    newContactSpec.lead && newContactSpec.spec
                      ? "bg-blue-600 text-white hover:bg-blue-700"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  <Plus className="w-3 h-3" />
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-600">
                Specify contact ranges for depth electrodes (e.g., &quot;1-5,7,9&quot; for contacts 1,2,3,4,5,7,9)
              </p>
            </div>
      </div>

      <InfoBox type="info" className="mt-4">
        <strong>Channel Selection:</strong> Choose which channels to include in the analysis. 
        Contact specifications allow you to select specific contacts on depth electrodes. 
        If no channels are selected, all available channels will be analyzed.
      </InfoBox>
    </BaseParameterSection>
  );
};

export default ChannelSelectionSection;
