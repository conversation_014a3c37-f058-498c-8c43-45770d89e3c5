"use client";

import React from "react";
import { BaseParameterSection } from "./BaseParameterSection";
import { InfoBox } from "../ui/InfoBox";
import { MontageConfig } from "@/types/eeg";

interface MontageSectionProps {
  parameters: MontageConfig;
  onUpdate: (parameters: MontageConfig) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: 'valid' | 'error';
  availableChannels: string[];
}

const MONTAGE_OPTIONS = [
  { 
    value: 'bipolar', 
    label: 'Bipolar', 
    description: 'Adjacent electrode pairs (e.g., C3-C4, F3-F4)' 
  },
  { 
    value: 'average', 
    label: 'Average Reference', 
    description: 'Each electrode referenced to the average of all electrodes' 
  },
  { 
    value: 'referential', 
    label: 'Referential', 
    description: 'All electrodes referenced to a single reference electrode' 
  },
];

const MontageSection: React.FC<MontageSectionProps> = ({
  parameters,
  onUpdate,
  errors,
  isExpanded,
  onToggle,
  status,
  availableChannels,
}) => {
  const updateMontageType = (type: 'bipolar' | 'average' | 'referential') => {
    onUpdate({
      ...parameters,
      type,
      // Clear reference channel if not referential
      reference_channel: type === 'referential' ? parameters.reference_channel : undefined,
    });
  };

  const updateReferenceChannel = (channel: string) => {
    onUpdate({
      ...parameters,
      reference_channel: channel,
    });
  };

  return (
    <BaseParameterSection
      title="Montage Configuration"
      status={status}
      errors={errors}
      isExpanded={isExpanded}
      onToggle={onToggle}
    >
      <div className="mt-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Montage Type
              </label>
              <div className="space-y-3">
                {MONTAGE_OPTIONS.map((option) => (
                  <label
                    key={option.value}
                    className={`flex items-start gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      parameters.type === option.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <input
                      type="radio"
                      name="montage-type"
                      value={option.value}
                      checked={parameters.type === option.value}
                      onChange={() => updateMontageType(option.value as 'bipolar' | 'average' | 'referential')}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">
                        {option.label}
                      </div>
                      <div className="text-sm text-gray-600">
                        {option.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {parameters.type === 'referential' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reference Channel
                </label>
                <select
                  value={parameters.reference_channel || ''}
                  onChange={(e) => updateReferenceChannel(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.some(error => error.includes('reference'))
                      ? "border-red-300"
                      : "border-gray-300"
                  }`}
                >
                  <option value="">Select reference channel...</option>
                  {availableChannels.map((channel) => (
                    <option key={channel} value={channel}>
                      {channel}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-600">
                  Choose a channel to use as the reference for all other channels
                </p>
              </div>
            )}
      </div>

      <InfoBox type="info" className="mt-4">
        <strong>Montage Selection:</strong> The montage determines how electrode signals are combined. 
        Bipolar montages are often preferred for HFO detection as they reduce common artifacts. 
        Referential montages may be useful when a clean reference electrode is available.
      </InfoBox>
    </BaseParameterSection>
  );
};

export default MontageSection;
