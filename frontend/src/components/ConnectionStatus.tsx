"use client";

import React from "react";
import { Card, Badge, Space, Typography, Progress, Alert, Statistic, Row, Col } from "antd";
import { WifiOutlined, DisconnectOutlined, ExclamationCircleOutlined, BarChartOutlined } from "@ant-design/icons";

const { Text, Title } = Typography;

interface ConnectionStatusProps {
  isConnected: boolean;
  progress?: number;
  dataPoints?: number;
  error?: string;
  compact?: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  progress = 0,
  dataPoints = 0,
  error,
  compact = false,
}) => {
  if (compact) {
    return (
      <Space size="small" align="center">
        <Badge 
          status={isConnected ? "success" : error ? "error" : "default"} 
          text={
            <Space size="small">
              <Text strong type={isConnected ? "success" : error ? "danger" : "secondary"}>
                {isConnected ? "Connected" : "Disconnected"}
              </Text>
              {progress > 0 && progress < 100 && (
                <Text type="secondary">• {Math.round(progress)}%</Text>
              )}
            </Space>
          }
        />
      </Space>
    );
  }

  return (
    <Card 
      size="small"
      className="shadow-sm"
      styles={{
        body: { padding: "16px" }
      }}
    >
      <Space direction="vertical" size="middle" className="w-full">
        {/* Header Section */}
        <Row align="middle" justify="space-between">
          <Col>
            <Space size="middle">
              <div className={`p-2 rounded-lg ${isConnected ? "bg-green-50" : error ? "bg-red-50" : "bg-gray-50"}`}>
                {isConnected ? (
                  <WifiOutlined style={{ fontSize: 20, color: "#52c41a" }} />
                ) : error ? (
                  <ExclamationCircleOutlined style={{ fontSize: 20, color: "#ff4d4f" }} />
                ) : (
                  <DisconnectOutlined style={{ fontSize: 20, color: "#8c8c8c" }} />
                )}
              </div>
              <div>
                <Title level={5} className="!mb-0">
                  Connection Status
                </Title>
                <Space size="small">
                  <Badge 
                    status={isConnected ? "success" : error ? "error" : "default"} 
                  />
                  <Text type={isConnected ? "success" : error ? "danger" : "secondary"}>
                    {isConnected
                      ? "WebSocket connected"
                      : error
                      ? "Connection error"
                      : "Waiting for connection..."}
                  </Text>
                </Space>
              </div>
            </Space>
          </Col>
          
          {isConnected && dataPoints > 0 && (
            <Col>
              <Statistic
                title={
                  <Space size="small">
                    <BarChartOutlined />
                    <Text type="secondary">Data Points</Text>
                  </Space>
                }
                value={dataPoints}
                valueStyle={{ fontSize: 20, color: "#000" }}
                formatter={(value) => value.toLocaleString()}
              />
            </Col>
          )}
        </Row>

        {/* Error Message */}
        {error && (
          <Alert
            message="Connection Error"
            description={error}
            type="error"
            showIcon
            closable
          />
        )}

        {/* Progress Bar */}
        {isConnected && progress > 0 && progress < 100 && (
          <div className="pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <Text type="secondary" className="text-sm">
                Processing Data
              </Text>
              <Text strong className="text-sm">
                {Math.round(progress)}%
              </Text>
            </div>
            <Progress
              percent={Math.round(progress)}
              strokeColor="#000000"
              showInfo={false}
              size="small"
            />
          </div>
        )}

        {/* Connection Info for Connected State */}
        {isConnected && !error && (
          <div className="pt-3 border-t border-gray-200">
            <Row gutter={16}>
              <Col span={12}>
                <Space direction="vertical" size="small">
                  <Text type="secondary" className="text-xs">Protocol</Text>
                  <Text strong>WebSocket</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space direction="vertical" size="small">
                  <Text type="secondary" className="text-xs">Status</Text>
                  <Badge status="processing" text="Live" />
                </Space>
              </Col>
            </Row>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default ConnectionStatus;