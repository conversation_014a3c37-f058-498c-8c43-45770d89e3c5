"use client";

import React from 'react';
import clsx from 'clsx';

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: boolean;
  helperText?: string;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  error = false,
  helperText,
  className,
  ...props
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
    </label>
    <input
      className={clsx(
        "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
        error ? "border-red-300" : "border-gray-300",
        className
      )}
      {...props}
    />
    {helperText && (
      <p className="mt-1 text-xs text-gray-600">{helperText}</p>
    )}
  </div>
);