"use client";

import React from 'react';
import { ChevronDown, ChevronUp, AlertCircle, CheckCircle } from 'lucide-react';

interface SectionHeaderProps {
  title: string;
  status: 'valid' | 'error';
  errorCount?: number;
  isExpanded: boolean;
  onToggle: () => void;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  status,
  errorCount = 0,
  isExpanded,
  onToggle,
}) => (
  <button
    onClick={onToggle}
    className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
  >
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-2">
        {status === 'error' ? (
          <AlertCircle className="w-4 h-4 text-red-500" />
        ) : (
          <CheckCircle className="w-4 h-4 text-green-500" />
        )}
        <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      </div>
      {errorCount > 0 && (
        <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
          {errorCount} error{errorCount > 1 ? 's' : ''}
        </span>
      )}
    </div>
    {isExpanded ? (
      <ChevronUp className="w-4 h-4 text-gray-500" />
    ) : (
      <ChevronDown className="w-4 h-4 text-gray-500" />
    )}
  </button>
);