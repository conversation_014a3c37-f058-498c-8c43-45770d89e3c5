"use client";

import React from 'react';

interface InfoBoxProps {
  title?: string;
  children: React.ReactNode;
  type?: 'info' | 'warning' | 'success';
  className?: string;
}

const variantStyles = {
  info: 'bg-blue-50 border-blue-200 text-blue-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  success: 'bg-green-50 border-green-200 text-green-800',
};

export const InfoBox: React.FC<InfoBoxProps> = ({ 
  title, 
  children, 
  type = 'info',
  className = ''
}) => (
  <div className={`p-3 border rounded-lg ${variantStyles[type]} ${className}`}>
    {title && <strong>{title}:</strong>} {children}
  </div>
);