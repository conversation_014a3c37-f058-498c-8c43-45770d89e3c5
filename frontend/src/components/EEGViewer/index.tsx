"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useWebSocket } from "@/contexts/WebSocketContext";
import ConnectionStatus from "@/components/ConnectionStatus";
import ProgressIndicator from "@/components/ProgressIndicator";
import { Layers, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download } from "lucide-react";
import clsx from "clsx";

const ChannelGrid = dynamic(() => import("./ChannelGrid"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <div className="text-center">
        <div className="animate-pulse">
          <Layers className="w-12 h-12 text-gray-400 mx-auto mb-3" />
        </div>
        <p className="text-sm text-gray-600">Loading visualization...</p>
      </div>
    </div>
  ),
});

const EEGViewer: React.FC = () => {
  const { isConnected, progress, chunkResults, error } = useWebSocket();
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);

  const allChannelData: Record<string, number[]> = {};

  chunkResults.forEach((chunk) => {
    if (chunk.channel_data) {
      Object.entries(chunk.channel_data).forEach(([channel, data]) => {
        if (!allChannelData[channel]) {
          allChannelData[channel] = [];
        }
        allChannelData[channel].push(...(data as number[]));
      });
    }
  });

  const channels = Object.keys(allChannelData);
  const visibleChannels = selectedChannels.length > 0 ? selectedChannels : channels;

  const totalDataPoints = Object.values(allChannelData).reduce((sum, data) => sum + data.length, 0);

  useEffect(() => {
    if (channels.length > 0 && selectedChannels.length === 0) {
      setSelectedChannels(channels.slice(0, Math.min(10, channels.length)));
    }
  }, [channels, selectedChannels.length]);

  const handleTimeNavigation = (direction: "prev" | "next") => {
    const shift = direction === "next" ? 10 : -10;
    setTimeWindow(([start, end]) => [Math.max(0, start + shift), Math.max(10, end + shift)]);
  };

  const handleZoom = (type: "in" | "out") => {
    const factor = type === "in" ? 0.5 : 2;
    setTimeWindow(([start, end]) => {
      const center = (start + end) / 2;
      const halfRange = ((end - start) / 2) * factor;
      return [Math.max(0, center - halfRange), center + halfRange];
    });
  };

  const toggleChannel = (channel: string) => {
    setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <div>
              <h1 className="text-xl font-bold text-black">EEG Signal Analysis</h1>
              <p className="text-sm text-gray-600">Real-time High-Frequency Oscillation Detection</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <ConnectionStatus isConnected={isConnected} progress={progress} dataPoints={totalDataPoints} error={error || undefined} compact />
            <ProgressIndicator progress={progress} label="Analysis" variant="medical" size="small" />
          </div>
        </div>
      </header>

      {channels.length > 0 ? (
        <div className="flex-1 flex overflow-hidden bg-white m-4 rounded-lg shadow-sm">
          <div className="flex-1 flex flex-col">
            <div className="flex-shrink-0 h-14 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-sm font-semibold text-gray-900">Channel View</h2>
                <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                  {visibleChannels.length}/{channels.length} channels
                </span>
              </div>

              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 bg-white rounded p-1">
                  <button
                    onClick={() => handleTimeNavigation("prev")}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                    title="Previous 10 seconds"
                  >
                    <ChevronLeft className="w-3 h-3 text-gray-600" />
                  </button>
                  <span className="px-2 text-xs font-medium text-gray-700">
                    {timeWindow[0]}s - {timeWindow[1]}s
                  </span>
                  <button
                    onClick={() => handleTimeNavigation("next")}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                    title="Next 10 seconds"
                  >
                    <ChevronRight className="w-3 h-3 text-gray-600" />
                  </button>
                </div>

                <div className="flex items-center gap-1 bg-white rounded p-1">
                  <button onClick={() => handleZoom("in")} className="p-1.5 hover:bg-gray-100 rounded transition-colors" title="Zoom in">
                    <ZoomIn className="w-4 h-4 text-gray-600" />
                  </button>
                  <button onClick={() => handleZoom("out")} className="p-1.5 hover:bg-gray-100 rounded transition-colors" title="Zoom out">
                    <ZoomOut className="w-4 h-4 text-gray-600" />
                  </button>
                </div>

                <button className="px-3 py-1 bg-black text-white text-xs rounded hover:bg-gray-800 transition-colors flex items-center gap-1">
                  <Download className="w-3 h-3" />
                  Export
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-auto">
              <ChannelGrid
                channelData={allChannelData}
                visibleChannels={visibleChannels}
                timeWindow={timeWindow}
                samplingRate={256}
                showHFOMarkers={false}
                hfoEvents={[]}
                channelHeight={80}
              />
            </div>
          </div>

          <div className="flex-shrink-0 w-64 bg-gray-50 border-l border-gray-200 overflow-hidden flex flex-col">
            <div className="p-3 border-b border-gray-200">
              <h3 className="text-xs font-semibold text-gray-900 flex items-center gap-2">
                <Layers className="w-3 h-3 text-gray-600" />
                Channel Selection
              </h3>
            </div>

            <div className="flex-1 overflow-y-auto p-3">
              <div className="space-y-1">
                {channels.map((channel) => (
                  <label
                    key={channel}
                    className={clsx(
                      "flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all",
                      "hover:bg-white",
                      selectedChannels.includes(channel) && "bg-white border border-gray-300"
                    )}
                  >
                    <input
                      type="checkbox"
                      checked={selectedChannels.includes(channel)}
                      onChange={() => toggleChannel(channel)}
                      className="w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500"
                    />
                    <div className="flex-1">
                      <span className="text-xs font-medium text-gray-700">{channel}</span>
                      <div
                        className="h-1 mt-1 rounded-full"
                        style={{
                          background: selectedChannels.includes(channel) ? "#000000" : "#d1d5db",
                        }}
                      />
                    </div>
                  </label>
                ))}
              </div>

              <div className="mt-3 pt-3 border-t border-gray-200">
                <button onClick={() => setSelectedChannels(channels)} className="w-full text-xs text-gray-700 hover:text-black font-medium">
                  Select All
                </button>
                <button onClick={() => setSelectedChannels([])} className="w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1">
                  Clear Selection
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center bg-white rounded-lg shadow-sm p-8">
            <div className="animate-pulse mb-4">
              <Layers className="w-16 h-16 text-gray-400 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{isConnected ? "Waiting for EEG Data" : "Connecting to Server"}</h3>
            <p className="text-sm text-gray-600">
              {isConnected ? "The analysis will begin shortly. Please wait..." : "Establishing WebSocket connection..."}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EEGViewer;
