'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import { HFOEvent } from '@/types/eeg';
import { PlotlyTrace } from '@/types/plotly';

interface ChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  showHFOMarkers?: boolean;
  hfoEvents?: HFOEvent[];
}

const ChannelRow: React.FC<ChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  showHFOMarkers = false,
  hfoEvents = [],
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    const timeAxis = data.map((_, i) => i / samplingRate);
    
    const startIdx = Math.floor(timeWindow[0] * samplingRate);
    const endIdx = Math.floor(timeWindow[1] * samplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const traces: PlotlyTrace[] = [{
      x: windowedTime,
      y: windowedData,
      type: 'scatter',
      mode: 'lines',
      name: channelName,
      line: { color: '#000000', width: 0.8 },
      hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    }];

    if (showHFOMarkers) {
      const channelHFOs = hfoEvents.filter(
        hfo => hfo.channel === channelName && 
        hfo.start_time >= timeWindow[0] && 
        hfo.start_time <= timeWindow[1]
      );

      if (channelHFOs.length > 0) {
        traces.push({
          x: channelHFOs.map(hfo => hfo.start_time),
          y: channelHFOs.map(() => 0),
          type: 'scatter',
          mode: 'markers',
          name: 'HFO',
          marker: {
            color: '#ef4444',
            size: 6,
            symbol: 'circle',
          },
          showlegend: false,
          hovertemplate: 'HFO Event<br>Time: %{x:.2f}s<extra></extra>',
        });
      }
    }

    return traces;
  }, [data, channelName, timeWindow, samplingRate, showHFOMarkers, hfoEvents]);

  const layout = {
    xaxis: {
      range: timeWindow,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      showline: false,
    },
    yaxis: {
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      showline: false,
      autorange: true,
    },
    height: height,
    margin: { l: 80, r: 10, t: 5, b: 5 },
    hovermode: 'x',
    showlegend: false,
    plot_bgcolor: '#ffffff',
    paper_bgcolor: '#ffffff',
    annotations: [{
      x: 0,
      y: 0.5,
      xref: 'paper',
      yref: 'paper',
      text: channelName,
      showarrow: false,
      xanchor: 'right',
      xshift: -10,
      font: {
        size: 11,
        color: '#4a5568',
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
      },
    }],
  };

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%', height: `${height}px` }}
      />
    </div>
  );
};

export default React.memo(ChannelRow);