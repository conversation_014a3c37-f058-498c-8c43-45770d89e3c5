"use client";

import React from "react";
import { Progress, Space, Typography, Badge } from "antd";
import { CheckCircleOutlined, LoadingOutlined } from "@ant-design/icons";

const { Text } = Typography;

interface ProgressIndicatorProps {
  progress: number;
  label?: string;
  showPercentage?: boolean;
  size?: "small" | "default" | "large";
  variant?: "default" | "gradient" | "medical";
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  label = "Processing",
  showPercentage = true,
  size = "default",
  variant = "default",
}) => {
  const isComplete = progress >= 100;

  const strokeColor = variant === "gradient" 
    ? { "0%": "#000000", "100%": "#333333" }
    : "#000000";

  const getStrokeWidth = () => {
    switch (size) {
      case "small": return 6;
      case "large": return 10;
      default: return 8;
    }
  };

  return (
    <Space direction="vertical" className="w-full">
      {label && (
        <div className="flex items-center justify-between">
          <Space size="small">
            {isComplete ? (
              <CheckCircleOutlined className="text-green-500" style={{ fontSize: 16 }} />
            ) : (
              <LoadingOutlined className="text-gray-600" style={{ fontSize: 16 }} />
            )}
            <Text strong={isComplete} type={isComplete ? "success" : undefined}>
              {label}
            </Text>
          </Space>
          {isComplete && (
            <Badge status="success" text="Complete" />
          )}
        </div>
      )}

      <Progress
        percent={Math.round(progress)}
        strokeColor={strokeColor}
        strokeWidth={getStrokeWidth()}
        showInfo={showPercentage}
        status={isComplete ? "success" : "active"}
        format={(percent) => (
          <Text strong className="text-sm">
            {percent}%
          </Text>
        )}
      />

      {variant === "medical" && progress > 0 && progress < 100 && (
        <div className="flex items-center justify-between text-xs">
          <Text type="secondary">
            Analyzing EEG data...
          </Text>
          <Text type="secondary">
            ~{Math.round((100 - progress) / 10)}s remaining
          </Text>
        </div>
      )}
    </Space>
  );
};

export default ProgressIndicator;