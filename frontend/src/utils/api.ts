import { AnalysisParameters, FileInfo } from "@/types/eeg";

const API_BASE_URL = "http://localhost:8000";

export class APIError extends Error {
  constructor(message: string, public status?: number, public errors?: string[]) {
    super(message);
    this.name = "APIError";
  }
}


/**
 * Get file information without starting analysis
 */
export async function getFileInfo(filepath: string): Promise<FileInfo> {
  // Use the analyze endpoint to get file info
  const response = await fetch(`${API_BASE_URL}/api/analyze`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      filepath,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new APIError(errorData.message || errorData.detail || "Failed to load file information", response.status, errorData.errors);
  }

  const data = await response.json();

  // Extract file info from the analyze response
  return {
    file_id: filepath,
    filename: filepath.split("/").pop() || filepath,
    start_date: data.file_info?.start_date || "",
    start_time: data.file_info?.start_time || "",
    end_date: "",
    end_time: "",
    sampling_rate: data.file_info?.sampling_rate || 256,
    channels: data.file_info?.channels || [],
    duration_seconds: data.file_info?.duration_seconds || 0,
  };
}

/**
 * Start analysis with parameters
 */
export async function startAnalysis(filepath: string, parameters: AnalysisParameters): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/api/analyze/start`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      filepath,
      parameters,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new APIError(errorData.message || errorData.detail || "Failed to start analysis", response.status, errorData.errors);
  }
}
