import { 
  ThresholdParameters, 
  FrequencyFilter, 
  TimeSegment, 
  MontageConfig,
  ChannelSelection 
} from '@/types/eeg';
import { THRESHOLD_CONSTRAINTS, FREQUENCY_OPTIONS, DATE_REGEX, TIME_REGEX } from './constants';

export function validateThresholds(thresholds: ThresholdParameters): string[] {
  const errors: string[] = [];

  Object.entries(THRESHOLD_CONSTRAINTS).forEach(([param, constraints]) => {
    const value = thresholds[param as keyof ThresholdParameters];
    
    if (typeof value !== 'number') {
      errors.push(`${param} must be a number`);
      return;
    }

    if (value < constraints.min || value > constraints.max) {
      errors.push(`${param} must be between ${constraints.min} and ${constraints.max}`);
    }
  });

  return errors;
}

export function validateFrequency(
  frequency: FrequencyFilter, 
  samplingRate?: number
): string[] {
  const errors: string[] = [];

  if (!FREQUENCY_OPTIONS.low_cutoff.includes(frequency.low_cutoff)) {
    errors.push(`Low cutoff ${frequency.low_cutoff}Hz is not a valid option`);
  }

  if (!FREQUENCY_OPTIONS.high_cutoff.includes(frequency.high_cutoff)) {
    errors.push(`High cutoff ${frequency.high_cutoff}Hz is not a valid option`);
  }

  if (frequency.low_cutoff >= frequency.high_cutoff) {
    errors.push('Low cutoff must be less than high cutoff');
  }

  if (samplingRate) {
    const nyquistFreq = samplingRate / 2;
    const maxUsableFreq = samplingRate / 3;

    if (frequency.high_cutoff > nyquistFreq) {
      errors.push(`High cutoff (${frequency.high_cutoff}Hz) exceeds Nyquist frequency (${nyquistFreq}Hz)`);
    }

    if (frequency.high_cutoff > maxUsableFreq) {
      errors.push(`High cutoff (${frequency.high_cutoff}Hz) exceeds maximum usable frequency (${maxUsableFreq.toFixed(1)}Hz)`);
    }
  }

  return errors;
}

export function validateMontage(
  montage: MontageConfig, 
  availableChannels?: string[]
): string[] {
  const errors: string[] = [];

  const validTypes = ['bipolar', 'average', 'referential'];
  if (!validTypes.includes(montage.type)) {
    errors.push(`Montage type must be one of: ${validTypes.join(', ')}`);
  }

  if (montage.type === 'referential') {
    if (!montage.reference_channel) {
      errors.push('Reference channel is required for referential montage');
    } else if (availableChannels && !availableChannels.includes(montage.reference_channel)) {
      errors.push(`Reference channel "${montage.reference_channel}" is not available`);
    }
  }

  return errors;
}

export function validateTimeSegment(
  timeSegment: TimeSegment,
  fileDuration?: number
): string[] {
  const errors: string[] = [];

  const validModes = ['entire_file', 'start_end_times', 'start_time_duration'];
  if (!validModes.includes(timeSegment.mode)) {
    errors.push(`Time segment mode must be one of: ${validModes.join(', ')}`);
  }

  if (timeSegment.mode !== 'entire_file') {
    if (timeSegment.start_date && !DATE_REGEX.test(timeSegment.start_date)) {
      errors.push('Start date must be in format dd.mm.yy');
    }

    if (timeSegment.end_date && !DATE_REGEX.test(timeSegment.end_date)) {
      errors.push('End date must be in format dd.mm.yy');
    }

    if (timeSegment.start_time && !TIME_REGEX.test(timeSegment.start_time)) {
      errors.push('Start time must be in format HH:MM:SS');
    }

    if (timeSegment.end_time && !TIME_REGEX.test(timeSegment.end_time)) {
      errors.push('End time must be in format HH:MM:SS');
    }

    if (timeSegment.mode === 'start_time_duration') {
      if (!timeSegment.duration_seconds || timeSegment.duration_seconds <= 0) {
        errors.push('Duration must be greater than 0 seconds');
      }

      if (fileDuration && timeSegment.duration_seconds > fileDuration) {
        errors.push(`Duration cannot exceed file duration (${fileDuration}s)`);
      }
    }
  }

  return errors;
}

function isValidContactSpecification(spec: string): boolean {
  if (!spec.trim()) return true;

  const parts = spec.split(',');
  for (const part of parts) {
    const trimmed = part.trim();
    if (trimmed.includes('-')) {
      const [start, end] = trimmed.split('-');
      if (!start || !end || isNaN(Number(start)) || isNaN(Number(end))) {
        return false;
      }
    } else {
      if (isNaN(Number(trimmed))) {
        return false;
      }
    }
  }
  return true;
}

export function validateChannelSelection(
  channelSelection: ChannelSelection,
  availableChannels?: string[]
): string[] {
  const errors: string[] = [];

  if (availableChannels) {
    const invalidLeads = channelSelection.selected_leads.filter(
      lead => !availableChannels.includes(lead)
    );

    if (invalidLeads.length > 0) {
      errors.push(`Invalid channels selected: ${invalidLeads.join(', ')}`);
    }
  }

  Object.entries(channelSelection.contact_specifications).forEach(([lead, spec]) => {
    if (spec && !isValidContactSpecification(spec)) {
      errors.push(`Invalid contact specification for ${lead}: "${spec}"`);
    }
  });

  return errors;
}