import { AnalysisParameters, FileInfo, ValidationErrors } from '@/types/eeg';
import {
  validateThresholds,
  validateFrequency,
  validateMontage,
  validateTimeSegment,
  validateChannelSelection
} from './validators';

export * from './validators';
export * from './constants';

export function validateAllParameters(
  parameters: AnalysisParameters,
  fileInfo?: FileInfo
): ValidationErrors {
  const errors: ValidationErrors = {};

  const thresholdErrors = validateThresholds(parameters.thresholds);
  if (thresholdErrors.length > 0) {
    errors.thresholds = thresholdErrors;
  }

  const frequencyErrors = validateFrequency(parameters.frequency, fileInfo?.sampling_rate);
  if (frequencyErrors.length > 0) {
    errors.frequency = frequencyErrors;
  }

  const montageErrors = validateMontage(parameters.montage, fileInfo?.channels);
  if (montageErrors.length > 0) {
    errors.montage = montageErrors;
  }

  const timeSegmentErrors = validateTimeSegment(parameters.time_segment, fileInfo?.duration_seconds);
  if (timeSegmentErrors.length > 0) {
    errors.time_segment = timeSegmentErrors;
  }

  const channelErrors = validateChannelSelection(parameters.channel_selection, fileInfo?.channels);
  if (channelErrors.length > 0) {
    errors.channel_selection = channelErrors;
  }

  return errors;
}

export function areParametersValid(errors: ValidationErrors): boolean {
  return Object.keys(errors).length === 0;
}