"use client";

import EEG<PERSON>iewer from "@/components/EEGViewer";
import FileUploadCard from "@/components/FileUploadCard";
import ParameterSettingsCard from "@/components/ParameterSettingsCard";
import StepNavigation from "@/components/StepNavigation";
import { WebSocketProvider } from "@/contexts/WebSocketContext";
import { AnalysisParameters, DEFAULT_PARAMETERS, FileInfo } from "@/types/eeg";
import { getFileInfo, startAnalysis } from "@/utils/api";
import { areParametersValid, validateAllParameters } from "@/utils/validation";
import { LineChartOutlined } from "@ant-design/icons";
import { Layout, message, Space, Typography } from "antd";
import { useCallback, useState } from "react";

const { Header, Content } = Layout;
const { Title } = Typography;

type AppState = "file-selection" | "settings-configuration" | "analysis";

export default function Home() {
  const [appState, setAppState] = useState<AppState>("file-selection");
  const [filepath, setFilepath] = useState("");
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [parameters, setParameters] = useState<AnalysisParameters>(DEFAULT_PARAMETERS);
  const [, setIsValid] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleFileSelect = async (selectedFilepath: string) => {
    setFilepath(selectedFilepath);
    setError("");

    if (!selectedFilepath) {
      setFileInfo(null);
      return;
    }

    try {
      setIsLoading(true);
      const info = await getFileInfo(selectedFilepath);
      setFileInfo(info);
      setAppState("settings-configuration");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load file information");
      setFileInfo(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleParametersChange = useCallback((newParameters: AnalysisParameters) => {
    setParameters(newParameters);
  }, []);

  const handleValidationChange = useCallback((valid: boolean) => {
    setIsValid(valid);
  }, []);

  const handleStartAnalysis = async () => {
    // Validate file and basic requirements
    if (!filepath || !fileInfo) {
      message.error("Please select a valid EDF file before starting analysis");
      setError("Please select a valid EDF file before starting analysis");
      return;
    }

    // Ensure we have default values for all parameters
    const finalParameters = {
      ...DEFAULT_PARAMETERS,
      ...parameters,
      // Ensure channel selection has defaults if empty
      channel_selection: {
        ...DEFAULT_PARAMETERS.channel_selection,
        ...parameters.channel_selection,
        selected_leads:
          parameters.channel_selection.selected_leads.length > 0
            ? parameters.channel_selection.selected_leads
            : fileInfo.channels?.slice(0, Math.min(10, fileInfo.channels.length)) || [],
      },
    };

    // Validate parameters with defaults applied
    const validationErrors = validateAllParameters(finalParameters, fileInfo);
    const isParametersValid = areParametersValid(validationErrors);

    if (!isParametersValid) {
      const errorCount = Object.values(validationErrors).flat().length;
      message.warning({
        content: `Found ${errorCount} validation issue${errorCount > 1 ? "s" : ""}. Using default values where possible.`,
        duration: 4,
      });

      // Update parameters with defaults
      setParameters(finalParameters);

      // Re-validate with defaults
      const newValidationErrors = validateAllParameters(finalParameters, fileInfo);
      const isNowValid = areParametersValid(newValidationErrors);

      if (!isNowValid) {
        const remainingErrors = Object.values(newValidationErrors).flat();
        message.error({
          content: `Cannot start analysis: ${remainingErrors.join(", ")}`,
          duration: 6,
        });
        setError(`Validation errors: ${remainingErrors.join(", ")}`);
        return;
      }
    }

    // Show success message and start analysis
    message.success({
      content: "Starting EEG analysis with configured parameters...",
      duration: 3,
    });

    try {
      setError("");
      setIsLoading(true);
      await startAnalysis(filepath, finalParameters);
      setAppState("analysis");

      message.info({
        content: "Analysis started successfully. Switching to real-time view...",
        duration: 2,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start analysis";
      message.error({
        content: `Analysis failed: ${errorMessage}`,
        duration: 6,
      });
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToFileSelection = () => {
    setAppState("file-selection");
    setFileInfo(null);
    setFilepath("");
    setParameters(DEFAULT_PARAMETERS);
    setIsValid(false);
    setError("");
  };

  const renderCurrentStep = () => {
    switch (appState) {
      case "file-selection":
        return <FileUploadCard onFileSelect={handleFileSelect} error={error} isLoading={isLoading} />;

      case "settings-configuration":
        return fileInfo ? (
          <ParameterSettingsCard
            fileInfo={fileInfo}
            onParametersChange={handleParametersChange}
            onValidationChange={handleValidationChange}
            onStartAnalysis={handleStartAnalysis}
            onBack={handleBackToFileSelection}
            initialParameters={parameters}
          />
        ) : null;

      case "analysis":
        return (
          <WebSocketProvider>
            <EEGViewer />
          </WebSocketProvider>
        );

      default:
        return null;
    }
  };

  return (
    <Layout className="h-screen w-screen flex flex-col">
      <Header className="bg-white border-b border-gray-200 px-6 md:px-8 h-16 flex items-center w-full flex-shrink-0">
        <div className="flex items-center justify-between h-full w-full max-w-7xl mx-auto">
          <Space size="large">
            <Title level={3} className="!mb-0 flex items-center gap-2 text-gray-900">
              <LineChartOutlined className="text-blue-600" />
              Biormika HFO Detector
            </Title>
          </Space>
          <div className="text-sm text-gray-500 hidden md:block">Real-time High-Frequency Oscillation Detection</div>
        </div>
      </Header>

      <Content className="flex-1 flex flex-col">
        <StepNavigation currentStep={appState} />
        <div className="flex-1 overflow-auto">{renderCurrentStep()}</div>
      </Content>
    </Layout>
  );
}
