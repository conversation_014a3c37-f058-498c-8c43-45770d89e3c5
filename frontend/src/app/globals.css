@import "tailwindcss";



:root {
  --gradient-primary: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  --gradient-secondary: linear-gradient(135deg, #333333 0%, #4a4a4a 100%);
  --gradient-accent: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-surface: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  --gradient-dark: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
  
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-accent: #f5f5f5;
  --bg-overlay: rgba(255, 255, 255, 0.98);
  
  --text-primary: #000000;
  --text-secondary: #4a4a4a;
  --text-muted: #9a9a9a;
  --text-accent: #ef4444;
  
  --border-light: #e5e5e5;
  --border-ultra-light: #f0f0f0;
  --border-accent: #000000;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 2px 4px 0 rgb(0 0 0 / 0.06);
  --shadow-lg: 0 4px 6px 0 rgb(0 0 0 / 0.08);
  --shadow-xl: 0 8px 12px 0 rgb(0 0 0 / 0.10);
  
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}


body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.gradient-text {
  color: var(--text-primary);
  font-weight: 600;
}

.card-soft {
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.card-soft:hover {
  box-shadow: var(--shadow-md);
}

.glass-card {
  background: var(--bg-overlay);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.gradient-button {
  background: var(--text-primary);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.gradient-button:hover {
  background: #1a1a1a;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.gradient-button:active {
  transform: translateY(0);
}

.gradient-button:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
}

.progress-bar {
  height: 4px;
  background: var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--text-primary);
  border-radius: var(--radius-xl);
  transition: width 0.3s ease;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.connected {
  background: var(--text-primary);
}

.status-dot.disconnected {
  background: var(--text-accent);
}

.upload-area {
  border: 2px dashed var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-accent);
  transition: all 0.2s ease;
}

/* Step Navigation Styles */
.step-navigation .ant-steps-item-process .ant-steps-item-icon {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.step-navigation .ant-steps-item-finish .ant-steps-item-icon {
  background-color: #10b981;
  border-color: #10b981;
}

.step-navigation .ant-steps-item-wait .ant-steps-item-icon {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.step-navigation .ant-steps-item-title {
  font-weight: 500;
  color: #374151;
}

.step-navigation .ant-steps-item-description {
  color: #6b7280;
  font-size: 0.875rem;
}

.upload-area:hover {
  border-color: var(--text-primary);
  background: var(--bg-secondary);
}

.upload-area.dragging {
  border-color: var(--text-primary);
  background: var(--bg-secondary);
  border-style: solid;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease;
}

.animate-slideIn {
  animation: slideIn 0.2s ease;
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.01);
}

.header-gradient {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
}

.chart-container {
  background: var(--bg-primary);
  width: 100%;
  height: 100%;
}

.input-modern {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: 10px 14px;
  transition: all 0.2s ease;
  font-size: 14px;
  width: 100%;
}

.input-modern:focus {
  outline: none;
  border-color: var(--text-primary);
  background: var(--bg-primary);
}

.input-modern::placeholder {
  color: var(--text-muted);
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  background: var(--bg-accent);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.badge.primary {
  background: var(--text-primary);
  color: white;
  border-color: var(--text-primary);
}

.divider {
  height: 1px;
  background: var(--border-light);
  margin: 20px 0;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-accent);
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

::-webkit-scrollbar-corner {
  background: var(--bg-accent);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}