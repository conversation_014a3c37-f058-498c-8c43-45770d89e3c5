export interface ThresholdParameters {
  amplitude1: number;
  amplitude2: number;
  peaks1: number;
  peaks2: number;
  duration: number;
  temporal_sync: number;
  spatial_sync: number;
}

export interface MontageConfig {
  type: "bipolar" | "average" | "referential";
  reference_channel?: string;
}

export interface FrequencyFilter {
  low_cutoff: number;
  high_cutoff: number;
}

export interface TimeSegment {
  mode: "entire_file" | "start_end_times" | "start_time_duration";
  start_date?: string;
  start_time?: string;
  end_date?: string;
  end_time?: string;
  duration_seconds?: number;
}

export interface ChannelSelection {
  selected_leads: string[];
  contact_specifications: Record<string, string>;
}

export interface AnalysisParameters {
  thresholds: ThresholdParameters;
  montage: MontageConfig;
  frequency: FrequencyFilter;
  time_segment: TimeSegment;
  channel_selection: ChannelSelection;
}

export interface FileInfo {
  file_id: string;
  filename: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  sampling_rate: number;
  channels: string[];
  duration_seconds: number;
}

export interface HFOEvent {
  channel: string;
  start_time: number;
  end_time: number;
  amplitude: number;
  frequency: number;
}

export interface ChunkResult {
  type: string;
  time_range: [number, number];
  hfo_events: HFOEvent[];
  channel_data: Record<string, number[]>;
  progress: number;
  chunk_number: number;
  total_chunks: number;
}

export interface WebSocketMessage {
  type: "preview_ready" | "chunk_complete" | "hfo_detected" | "analysis_complete" | "error" | "status_update";
  data?: ChunkResult | FileInfo | HFOEvent[] | Record<string, unknown>;
  message?: string;
  status?: string;
  progress?: number;
}

// Validation error structure
export interface ValidationErrors {
  [key: string]: string[];
}

// Default parameters matching backend defaults
export const DEFAULT_PARAMETERS: AnalysisParameters = {
  thresholds: {
    amplitude1: 2,
    amplitude2: 2,
    peaks1: 6,
    peaks2: 3,
    duration: 10,
    temporal_sync: 10,
    spatial_sync: 10,
  },
  montage: {
    type: "bipolar",
  },
  frequency: {
    low_cutoff: 50,
    high_cutoff: 300,
  },
  time_segment: {
    mode: "entire_file",
  },
  channel_selection: {
    selected_leads: [],
    contact_specifications: {},
  },
};
