from PyQt5.QtWidgets import (
    QApplication, QAction, QMainWindow, QWidget, QLabel, QPushButton, QCheckBox, QComboBox,
    QVBoxLayout, QGridLayout, QLineEdit, QRadioButton, QGroupBox, QTextEdit, QFileDialog, QMessageBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QHBoxLayout, QScrollArea, QSizePolicy, QSplitter, QProgressDialog, QProgressBar, QDialog
)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt,QTimer
import os
from glob import glob
from datetime import datetime, timedelta
import sys
import re, json, math, time
import numpy as np
import traceback
from hfo_analysis import run_hfo_algorithm
from plotAndSaveHfoV2 import plotAndSaveHfo
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from pyedfreader import edfread
from browse_files import browse_edf
from collections import defaultdict
from fpdf import FPDF
from PIL import Image


def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class HfoDetectionApp(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowIcon(QIcon(resource_path("biormika_icon.ico")))
        self.setWindowTitle("Biormika HFO Dectector")
        self.setGeometry(100, 100, 1028, 766)
        self.state = {}
        self.edfFiles = []
        self.edfHeaders = {}
        self.edfPaths = []
        self.selectedEdfFile = ""
        self.selectedHeader = {}
        # This will store dynamic categories
        self.categories = {}

        self.initUI()

    def initUI(self):
        # Main widget
        main_widget = QWidget(self)
        self.setCentralWidget(main_widget)
        
        # Layouts
        main_layout = QVBoxLayout(main_widget)
        grid_layout = QGridLayout()
        
        # Labels and Feedback area
        self.folder_label = QLabel("Folder path for loading EDF files", self)
        self.label = QLabel("Select file and specify segment", self)
        self.feedback = QTextEdit(self)
        self.feedback.setReadOnly(True)

        # Available files table
        headers = [" Selected ", " File ", " Start date ", " Start time ", " End date ", " End time ", "Sampling rate", "sort"]
        self.available_files_table = QTableWidget(0, 8, self)
        self.available_files_table.setHorizontalHeaderLabels(headers)
        # self.available_files_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        # Set resize mode for the columns
        header = self.available_files_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # "Selected" column adjusts to its content
        header.setSectionResizeMode(1, QHeaderView.Stretch)          # "File" column stretches
        header.setSectionResizeMode(2, QHeaderView.Stretch)          # "Start date" column stretches
        header.setSectionResizeMode(3, QHeaderView.Stretch)          # "Start time" column stretches
        header.setSectionResizeMode(4, QHeaderView.Stretch)          # "End date" column stretches
        header.setSectionResizeMode(5, QHeaderView.Stretch)          # "End time" column stretches
        header.setSectionResizeMode(6, QHeaderView.Stretch)          # "Sampling rate" column stretches
        # Hide the sorting column (column index 7)
        self.available_files_table.setColumnHidden(7, True)
        self.available_files_table.horizontalHeader().sectionClicked.connect(self.handle_section_clicked)

        # Buttons

        self.save_setting_button = QPushButton("Save Settings", self)
        self.reset_setting_button = QPushButton("Load Default Settings", self)
        self.browse_button = QPushButton("Browse", self)
        self.analyze_button = QPushButton("Analyze", self)
        self.cancel_button = QPushButton("Cancel", self)
        self.all_button = QPushButton("Select/Deselect All Leads", self)  # "All" button to select/deselect all leads
        
        # Create a horizontal layout for the "Montage", "Frequency", and "Specify Segments" sections
        hbox_layout = QHBoxLayout()

        # Add "Select HFO Detection Thresholds" to the very left
        self.add_hfo_detection_thresholds(hbox_layout)

        # Then add "Select Montage for Analysis" in the mid left
        self.create_montage_radio_buttons(hbox_layout)

        # Next, frequency selection in the middle right
        self.add_frequency_selection(hbox_layout)

        # Add "Specify Segments" to the very right of the frequency section
        self.add_segment_selection_options(hbox_layout)

        # Leads panel with checkboxes (for dynamic labels)

        self.lead_group_box = QGroupBox("Select contacts to analyze for the available leads (enter individual contact numbers separated by comma or range separated by dash)")
        self.lead_layout = QGridLayout()
        self.lead_checkboxes = []
        self.lead_edit_fields = []
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.lead_group_box)
        scroll_area.setFixedHeight(200)

        self.lead_group_box.setLayout(self.lead_layout)
        grid_layout.addWidget(scroll_area, 0, 0)
        
        # Adding components to layout
        main_layout.addWidget(self.folder_label)
        main_layout.addWidget(self.label)
        main_layout.addWidget(self.available_files_table)
        main_layout.addLayout(hbox_layout)  # Add the horizontal layout to the main layout
        main_layout.addLayout(grid_layout)

                
        # Add buttons
        # Create a grid layout for the buttons
        button_layout = QGridLayout()

        # Add buttons to the grid layout
        button_layout.addWidget(self.save_setting_button,0, 0)
        button_layout.addWidget(self.reset_setting_button,0, 1)
        button_layout.addWidget(self.browse_button, 0, 2)  # Row 0, Column 1
        button_layout.addWidget(self.all_button, 0, 3)  # Row 0, Column 0
        button_layout.addWidget(self.analyze_button, 0, 4)  # Row 1, Column 1
        button_layout.addWidget(self.cancel_button, 0, 5)  # Row 1, Column 0


        # Add the button layout to the main layout
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.feedback)

        # Connect events
        self.save_setting_button.clicked.connect(self.save_state)
        self.reset_setting_button.clicked.connect(self.reset_state)
        self.all_button.clicked.connect(self.toggle_all_leads)
        self.browse_button.clicked.connect(self.browse_files)
        self.analyze_button.clicked.connect(self.analyze)
        self.cancel_button.clicked.connect(self.cancel)


    def get_settings_folder(self):
        # If running as an executable, save to Desktop/hifod/settings
        if getattr(sys, 'frozen', False):
            return os.path.join(os.path.expanduser("~"), "Desktop", "hifod", "settings")
        else:
            # Otherwise, save settings in a subfolder relative to the script
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), "hifod", "settings")

    def get_reports_folder(self):
        # If running as an executable, save to Desktop/hifod/settings
        if getattr(sys, 'frozen', False):
            return os.path.join(os.path.expanduser("~"), "Desktop", "hifod", "reports")
        else:
            # Otherwise, save settings in a subfolder relative to the script
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), "hifod", "reports")


    def load_state(self):
        """Load the state from a saved JSON file."""
        if not self.selectedEdfFile:
            QMessageBox.critical(self, "Error", "No EDF file selected. Cannot load settings.")
            return

        file_name = os.path.splitext(os.path.basename(self.selectedEdfFile))[0]
        # Determine the base directory based on how the app is run
        if getattr(sys, 'frozen', False):  # If running as a bundled executable
            self.base_path = os.path.join(os.path.expanduser("~"), "Desktop")  # if run as exe, save settings and report to desktop
        else:
            self.base_path = os.path.dirname(os.path.abspath(__file__))  # Script directory

        print(f"base_path: {self.base_path}")

        # self.setting_path = self.base_path + f"/hifod/settings/{file_name}.json"
        self.setting_path = os.path.join(self.base_path, "hifod", "settings", f"{file_name}.json")

        print(f"setting_path:{self.setting_path}")

        if os.path.exists(self.setting_path):
            with open(self.setting_path, 'r') as file:
                try:
                    self.state = json.load(file)

                    # Apply thresholds
                    for i, (_, dropdown) in enumerate(self.dropdown_menus):
                        dropdown.setCurrentIndex(self.dropdown_menus[i][1].findText(str(self.state["thresholds"][i])))

                    # Apply frequency options
                    self.low_filter_dropdown.setCurrentIndex(
                        self.low_filter_dropdown.findText(str(self.state["frequencies"]["low"]))
                    )
                    self.high_filter_dropdown.setCurrentIndex(
                        self.high_filter_dropdown.findText(str(self.state["frequencies"]["high"]))
                    )

                    # Apply segments
                    self.entirefile_button.setChecked(self.state["segments"]["entire_file"])
                    self.startendtimes_button.setChecked(not self.state["segments"]["entire_file"])
                    self.starttimesec_button.setChecked(not self.state["segments"]["entire_file"])

                    segment_times = self.state["segments"]["start_end_times"]
                    self.EditField_StartDate1.setText(segment_times["start_date"])
                    self.EditField_StartTime1.setText(segment_times["start_time"])
                    self.EditField_EndDate1.setText(segment_times["end_date"])
                    self.EditField_EndTime1.setText(segment_times["end_time"])

                    segment_time_sec = self.state["segments"]["start_time_sec"]
                    self.EditField_StartDate2.setText(segment_time_sec["start_date"])
                    self.EditField_StartTime2.setText(segment_time_sec["start_time"])
                    self.EditField_Sec.setText(segment_time_sec["seconds"])

                    self.feedback.append("Settings loaded.")
                except json.JSONDecodeError:
                    self.feedback.append("Error: Could not decode the Setting file.")
        else:
            self.feedback.append("No Setting file found. Loading default values.")
            self.reset_state()


    def save_state(self):
        """Save the current state to a JSON file."""
        # Get the filename based on the selected EDF file
        if not self.selectedEdfFile:
            QMessageBox.critical(self, "Error", "No EDF file selected. Cannot save Settings.")
            return

        file_name = os.path.splitext(os.path.basename(self.selectedEdfFile))[0]
        save_path = self.setting_path

        # Gather state
        self.state = {
            "thresholds": [int(dropdown.currentText()) for _, dropdown in self.dropdown_menus],
            "frequencies": {
                "low": int(self.low_filter_dropdown.currentText()),
                "high": int(self.high_filter_dropdown.currentText()),
            },
            "segments": {
                "entire_file": self.entirefile_button.isChecked(),
                "start_end_times": {
                    "start_date": self.EditField_StartDate1.text(),
                    "start_time": self.EditField_StartTime1.text(),
                    "end_date": self.EditField_EndDate1.text(),
                    "end_time": self.EditField_EndTime1.text(),
                },
                "start_time_sec": {
                    "start_date": self.EditField_StartDate2.text(),
                    "start_time": self.EditField_StartTime2.text(),
                    "seconds": self.EditField_Sec.text(),
                },
            },
        }

        # Save to file
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        print(f"Directory created (if not exists): {os.path.dirname(save_path)}")

        with open(save_path, 'w') as file:
            json.dump(self.state, file, indent=4)
        self.feedback.append(f"Settings saved to {save_path}")


    def reset_state(self):
        """Reset all fields to their default values."""
        # Reset thresholds to their defaults
        for i, (_, dropdown) in enumerate(self.dropdown_menus):
            default_index = dropdown.property("default_index")
            if default_index is not None:
                dropdown.setCurrentIndex(default_index)

        # Reset frequency options to their defaults
        self.low_filter_dropdown.setCurrentIndex(self.low_filter_dropdown.findText("50"))  # Default to 50 Hz
        self.high_filter_dropdown.setCurrentIndex(self.high_filter_dropdown.findText("300"))  # Default to 300 Hz

        # Reset segment options
        self.entirefile_button.setChecked(True)
        self.startendtimes_button.setChecked(False)
        self.starttimesec_button.setChecked(False)


        hdr = self.get_edf_info(self.selectedEdfFile)
        if hdr:
            session_duration = timedelta(seconds=hdr['records'] * hdr['duration'])
            start_datetime = datetime.strptime(hdr['startdate'] + " " + hdr['starttime'], "%y.%m.%d %H.%M.%S")
            end_datetime = start_datetime + session_duration
            seconds = int(session_duration.total_seconds())
        # Clear all segment text fields
        self.EditField_StartDate1.setText(start_datetime.strftime("%y.%m.%d"))
        self.EditField_StartTime1.setText(start_datetime.strftime("%H:%M:%S"))
        self.EditField_EndDate1.setText(end_datetime.strftime("%y.%m.%d"))
        self.EditField_EndTime1.setText(end_datetime.strftime("%H:%M:%S"))
        self.EditField_StartDate2.setText(start_datetime.strftime("%y.%m.%d"))
        self.EditField_StartTime2.setText(start_datetime.strftime("%H:%M:%S"))
        self.EditField_Sec.setText(str(seconds))

        self.feedback.append("Settings reset to default values.")


    def add_hfo_detection_thresholds(self, layout):
        # GroupBox to contain the threshold dropdowns and labels
        thresholds_box = QGroupBox("Select HFO Detection Thresholds")
        thresholds_layout = QVBoxLayout()

        # Dictionary to hold labels and their corresponding options
        threshold_options = [
            {"label": "Amplitude 1: HFO amp >= energy signal by: (times of std)", "options": range(2, 6), "default": 2},
            {"label": "Amplitude 2: HFO amp >= mean baseline signal by:(times of std)", "options": range(2, 6), "default": 2},
            {"label": "Peaks 1: Number of peaks in HFO >= Amplitude 1: (# peaks)", "options": range(2, 9), "default": 6},
            {"label": "Peaks 2: Number of peaks in HFO >= Amplitude 2: (# peaks)", "options": range(2, 7), "default": 3},
            {"label": "Duration: HFO length >= (? ms)", "options": range(5, 16), "default": 10},
            {"label": "Temporal synchronization: Inter HFO interval in any channel <= (? ms)", "options": range(5, 13), "default": 10},
            {"label": "Spatial synchronization: Inter HFO interval across channels <= (? ms)", "options": range(5, 13), "default": 10}
        ]

        self.dropdown_menus = [] # List to hold the dropdowns
        # A common width for dropdowns
        dropdown_width = 100
        for threshold in threshold_options:
            h_layout = QHBoxLayout()  # Horizontal layout for each label and dropdown
            
            # Add label to left
            label = QLabel(threshold["label"])
            h_layout.addWidget(label)
            
            # Add dropdown to right
            dropdown = QComboBox()

            # Set dropdown width
            dropdown.setFixedWidth(dropdown_width)

            for option in threshold["options"]:
                dropdown.addItem(str(option))
                # Set the default selection based on the "default" value
                if threshold["default"] in threshold["options"]:
                    default_index = threshold["options"].index(threshold["default"])
                    dropdown.setCurrentIndex(default_index)
            h_layout.addWidget(dropdown)

            # Store dropdown for later access      
            self.dropdown_menus.append((label,dropdown))
            
            # Add this row to the main thresholds layout
            thresholds_layout.addLayout(h_layout)

        # Add completed layout with dropdowns to GroupBox and then add to main layout
        thresholds_box.setLayout(thresholds_layout)
        layout.addWidget(thresholds_box)


    def get_selected_threshold_values(self):
        # Loop through each (label, dropdown) pair to get values
        for label, dropdown in self.dropdown_menus:
            label_text = label.text()
            selected_value = int(dropdown.currentText())
            print(f"{label_text}{selected_value}")
        selected_values = [int(dropdown.currentText()) for _, dropdown in self.dropdown_menus]
        return selected_values


    def create_montage_radio_buttons(self, layout):
        group_box = QGroupBox("Select montage for analysis")
        vbox = QVBoxLayout()
        self.bipolar_radio = QRadioButton("Bipolar", self)
        self.bipolar_radio.setChecked(True)
        self.average_radio = QRadioButton("Average", self)
        self.referential_radio = QRadioButton("Referential", self)

        self.referential_edit = QLineEdit(self)
        self.referential_edit.setEnabled(False)

        self.bipolar_radio.toggled.connect(self.toggle_referential_input)

        vbox.addWidget(self.bipolar_radio)
        vbox.addWidget(self.average_radio)
        vbox.addWidget(self.referential_radio)
        vbox.addWidget(self.referential_edit)
        group_box.setLayout(vbox)
        layout.addWidget(group_box)

    def toggle_referential_input(self):
        """Toggle the enabling of the referential input field based on the selected montage."""
        self.referential_edit.setEnabled(self.referential_radio.isChecked())

        # If the referential montage is selected, process the input
        if self.referential_radio.isChecked():
            selected_montage = self.calc_selected_montage()
            if selected_montage:
                self.feedback.append(f"Referential montage selected: {selected_montage}")
            else:
                self.feedback.append("No valid referential montage entered.")

    def calc_selected_montage(self):
        """Function to get the selected montage based on the selected radio button."""
        # Check which montage is selected
        if self.bipolar_radio.isChecked():
            # Bipolar montage selected (default)
            return "Bipolar", None  # Return None for user_ref if not applicable
        
        elif self.average_radio.isChecked():
            # Average montage selected
            return "Average", None  # Return None for user_ref if not applicable
        
        elif self.referential_radio.isChecked() and self.referential_edit.text():
            # Referential montage selected, process the input from referential_edit field
            ref_input = self.referential_edit.text()
            ref_indices = []
            ref_ids = ref_input.split(',')
            
            for ref_id in ref_ids:
                interval = ref_id.split('-')
                if len(interval) == 1:
                    ref_indices.append(int(interval[0]))  # Single reference
                else:
                    ref_indices.extend(range(int(interval[0]), int(interval[1]) + 1))  # Range of references

            # Construct the referential montage string with indices
            return "Referential", [f"Ref{index}" for index in ref_indices]  # Return the user_ref as the list of references

        # Default case if no valid montage is selected
        return None, None  # Ensure it returns two values


    def categorize_labels(self, labels):
        """Categorize labels dynamically based on prefixes ('P' or 'POL')."""
        self.categories = {}

        for label in labels:
            # Extract the prefix from the label, either 'P' or 'POL'
            match = re.match(r'^(P|POL)\s([A-Za-z]+)', label)
            if match:
                prefix = match.group(2)  # Capture the actual prefix (e.g., 'RG', 'MARK')
                if prefix not in self.categories:
                    self.categories[prefix] = []
                self.categories[prefix].append(label)

        # dynamic categories based on the label prefixes
        self.update_leads_panel()


    def update_leads_panel(self):
        """Update the leads panel with categorized checkboxes and edit fields."""
        self.clear_leads_panel()  # Clear the current layout

        row = 0
        no_show_list = ["EKG", "REF","E","C"]
        for category, labels in self.categories.items():
            if category not in no_show_list:
                # print(category)
                checkbox = QCheckBox(f"{category} ({len(labels)} channels)", self)  #f"{category} ({len(labels)} channels)"

                edit_field = QLineEdit(self)
                edit_field.setEnabled(False)

                # Toggle the field based on the checkbox state
                checkbox.toggled.connect(lambda state, idx=row: self.toggle_lead_input(idx, state))

                self.lead_layout.addWidget(checkbox, row, 0)
                self.lead_layout.addWidget(edit_field, row, 1)
                self.lead_checkboxes.append(checkbox)
                self.lead_edit_fields.append(edit_field)

                row += 1

    def clear_leads_panel(self):
        """Clear the current lead checkboxes and edit fields."""
        for i in reversed(range(self.lead_layout.count())):
            widget = self.lead_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.lead_checkboxes.clear()
        self.lead_edit_fields.clear()

    def toggle_lead_input(self, index, state):
        """Enable or disable the corresponding edit field based on the checkbox state."""
        self.lead_edit_fields[index].setEnabled(state)

    def calc_selected_channel_labels(self,label_list):
        """Function to get the labels for the selected leads and their specified channels."""
        labels = []
        # print(f"label_list: {label_list}")
        grouped_labels = defaultdict(list)
        for label in label_list:
            match = re.match(r'^(?:POL |P )(\w+?)(\d+)$', label)
            if match:
                group, number = match.groups()
                grouped_labels[group].append(int(number))

        for idx, checkbox in enumerate(self.lead_checkboxes):
            editfield = self.lead_edit_fields[idx]

            if checkbox.isChecked() and checkbox.text() != "??":

                label = checkbox.text().strip()
                # print(f"label (channel) removed? : {label}")
                max_channel_id = int(re.search(r'\d+', label).group())
                # print(f"max_channel_id:{max_channel_id}")
                label = re.sub(r'^(POL |P )|(\d+)|( )', '', label)  # Clean the label, but preserve the actual name
                label = label.replace('(channels)', '').strip()  # Remove '(channels)' and strip any extra spaces
                # print(f"final label: {label}")
                for group, numbers in grouped_labels.items():
                    if label == group:
                        starting_num =  min(numbers)

                # Process the edit field to extract contact numbers or ranges
                if not editfield.text():
                    indices = list(range(starting_num, starting_num+ max_channel_id))  # Default range from 1 to maxChannelId
                else:
                    indices = []
                    channel_ids = editfield.text().split(',')
                    for channel_id in channel_ids:
                        interval = channel_id.split('-')
                        if len(interval) == 1:
                            indices.append(int(interval[0]))  # Single channel
                        else:
                            indices.extend(range(int(interval[0]), int(interval[1]) + 1))  # Range of channels

                # Combine the lead label with indices
                for index in indices:
                    labels.append(f"{label}{index}")

        return labels


    def add_frequency_selection(self, layout):
        """Add dropdown boxes for frequency band selection."""
        group_box = QGroupBox("Select frequency band for analysis")
        vbox = QVBoxLayout()

        # Dropdown options for low and high filters
        low_filter_options = [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250]
        high_filter_options = [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660]

        # Horizontal layout for Low Filter selection
        low_filter_layout = QHBoxLayout()
        low_filter_label = QLabel("Low cutoff filter (Hz):")
        self.low_filter_dropdown = QComboBox()
        for value in low_filter_options:
            self.low_filter_dropdown.addItem(str(value))
        self.low_filter_dropdown.setCurrentIndex(low_filter_options.index(50))  # Set default to 1 Hz
        low_filter_layout.addWidget(low_filter_label)
        low_filter_layout.addWidget(self.low_filter_dropdown)

        # Horizontal layout for High Filter selection
        high_filter_layout = QHBoxLayout()
        high_filter_label = QLabel("High cutoff filter (Hz):")
        self.high_filter_dropdown = QComboBox()
        for value in high_filter_options:
            self.high_filter_dropdown.addItem(str(value))
        self.high_filter_dropdown.setCurrentIndex(high_filter_options.index(300))  # Set default to 600 Hz
        high_filter_layout.addWidget(high_filter_label)
        high_filter_layout.addWidget(self.high_filter_dropdown)

        # Add low and high filter layouts to the main vertical layout
        vbox.addLayout(low_filter_layout)

        info_label = QLabel("High cutoff filter should <= 1/3 of sampling rate")
        info_label.setStyleSheet("color: gray;")
        vbox.addWidget(info_label)

        vbox.addLayout(high_filter_layout)



        group_box.setLayout(vbox)
        layout.addWidget(group_box)

    def get_cutoff_freqs(self, frequency_threshold):
        """Return the low and high cutoff frequencies based on the selected dropdown values."""
        low_cutoff = int(self.low_filter_dropdown.currentText())
        high_cutoff = int(self.high_filter_dropdown.currentText())

        if low_cutoff >= high_cutoff:
            self.freq_errormsg(frequency_threshold,high_cutoff,low_cutoff)
            raise ValueError(f"Invalid Sampling Rate and Filter Combination")
        if frequency_threshold < 2000 and high_cutoff > 600:
            self.freq_errormsg(frequency_threshold,high_cutoff,None)
            raise ValueError(f"Invalid Sampling Rate and Filter Combination")
        elif frequency_threshold < 1000 and high_cutoff > 330:
            self.freq_errormsg(frequency_threshold,high_cutoff,None)
            raise ValueError(f"Invalid Sampling Rate and Filter Combination")
        elif frequency_threshold < 500 and high_cutoff > 160:
            self.freq_errormsg(frequency_threshold,high_cutoff,None)
            raise ValueError(f"Invalid Sampling Rate and Filter Combination")
        elif frequency_threshold < 350 and high_cutoff > 80:
            self.freq_errormsg(frequency_threshold,high_cutoff,None)
            raise ValueError(f"Invalid Sampling Rate and Filter Combination")

        self.freqs = [low_cutoff, high_cutoff]
        return self.freqs

    def freq_errormsg(self, frequency_threshold, high_cutoff, low_cutoff):
        
        if low_cutoff is None or low_cutoff == '':
            error_message = (f"Sampling rate ({frequency_threshold}) is not high enough for the selected high cutoff filter value ({high_cutoff}).")
        else:
            error_message = (f"Low filter value({low_cutoff})must be less than High filter value({high_cutoff})")

        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("Error")
        msg_box.setText("Invalid Sampling Rate and Filter Combination")
        msg_box.setInformativeText(error_message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    def add_segment_selection_options(self, layout):
        """Add radio buttons and editable fields for segment selection."""
        group_box = QGroupBox("Specify Segments")
        vbox = QVBoxLayout()
        hbox_startdatetime1 = QHBoxLayout()
        hbox_enddatetime1 = QHBoxLayout()
        hbox_startdatetime2 = QHBoxLayout()
        # Create Entire file radio button
        self.entirefile_button = QRadioButton("Entire file")
        self.entirefile_button.setChecked(True)  # Default selection

        # Create Start/End times radio button
        self.startendtimes_button = QRadioButton("Start/End times")

        # Create Start time/Sec radio button
        self.starttimesec_button = QRadioButton("Start time/Sec")

        # Add buttons to the layout
        vbox.addWidget(self.entirefile_button)

        # Editable fields for start/end time inputs
        vbox.addWidget(self.startendtimes_button)

        self.EditField_StartDate1 = QLineEdit(self)
        self.EditField_StartDate1.setPlaceholderText('Start Date:(dd.mm.yy)')
        self.EditField_StartTime1 = QLineEdit(self)
        self.EditField_StartTime1.setPlaceholderText('Start Time:(HH:MM:SS)')
        self.EditField_EndDate1 = QLineEdit(self)
        self.EditField_EndDate1.setPlaceholderText('End Date:(dd.mm.yy)')
        self.EditField_EndTime1 = QLineEdit(self)
        self.EditField_EndTime1.setPlaceholderText('End Time:(HH:MM:SS)')
        hbox_startdatetime1.addWidget(self.EditField_StartDate1)
        hbox_startdatetime1.addWidget(self.EditField_StartTime1)
        hbox_enddatetime1.addWidget(self.EditField_EndDate1)
        hbox_enddatetime1.addWidget(self.EditField_EndTime1)

        # Add editable fields to layout
        vbox.addLayout(hbox_startdatetime1)
        vbox.addLayout(hbox_enddatetime1)

        # Editable fields for time/sec input
        vbox.addWidget(self.starttimesec_button)
        self.EditField_StartDate2 = QLineEdit(self)
        self.EditField_StartDate2.setPlaceholderText('Start Date:(dd.mm.yy)')
        self.EditField_StartTime2 = QLineEdit(self)
        self.EditField_StartTime2.setPlaceholderText('Start Time:(HH:MM:SS)')
        self.EditField_Sec = QLineEdit(self)
        self.EditField_Sec.setPlaceholderText('Seconds')
        hbox_startdatetime2.addWidget(self.EditField_StartDate2)
        hbox_startdatetime2.addWidget(self.EditField_StartTime2)

        # Add editable fields to layout
        vbox.addLayout(hbox_startdatetime2)
        vbox.addWidget(self.EditField_Sec)

        group_box.setLayout(vbox)
        layout.addWidget(group_box)

        # Connect radio buttons to toggle the corresponding fields
        self.entirefile_button.toggled.connect(self.handle_segment_selection)
        self.startendtimes_button.toggled.connect(self.handle_segment_selection)
        self.starttimesec_button.toggled.connect(self.handle_segment_selection)

        # Initially disable time fields for other than "Entire file"
        self.EditField_StartTime1.setEnabled(False)
        self.EditField_StartDate1.setEnabled(False)
        self.EditField_EndDate1.setEnabled(False)
        self.EditField_EndTime1.setEnabled(False)
        self.EditField_StartDate2.setEnabled(False)
        self.EditField_StartTime2.setEnabled(False)
        self.EditField_Sec.setEnabled(False)

    def handle_segment_selection(self):
        """Handle the radio button selection to enable/disable relevant fields."""
        if self.entirefile_button.isChecked():
            self.EditField_StartTime1.setEnabled(False)
            self.EditField_StartDate1.setEnabled(False)
            self.EditField_EndDate1.setEnabled(False)
            self.EditField_EndTime1.setEnabled(False)
            self.EditField_StartDate2.setEnabled(False)
            self.EditField_StartTime2.setEnabled(False)
            self.EditField_Sec.setEnabled(False)
        elif self.startendtimes_button.isChecked():
            self.EditField_StartTime1.setEnabled(True)
            self.EditField_StartDate1.setEnabled(True)
            self.EditField_EndDate1.setEnabled(True)
            self.EditField_EndTime1.setEnabled(True)
            self.EditField_StartDate2.setEnabled(False)
            self.EditField_StartTime2.setEnabled(False)
            self.EditField_Sec.setEnabled(False)
        elif self.starttimesec_button.isChecked():
            self.EditField_StartTime1.setEnabled(False)
            self.EditField_StartDate1.setEnabled(False)
            self.EditField_EndDate1.setEnabled(False)
            self.EditField_EndTime1.setEnabled(False)
            self.EditField_StartDate2.setEnabled(True)
            self.EditField_StartTime2.setEnabled(True)
            self.EditField_Sec.setEnabled(True)

    def toggle_all_leads(self):
        """Function to toggle all lead checkboxes."""
        all_selected = all(checkbox.isChecked() for checkbox in self.lead_checkboxes)
        for checkbox in self.lead_checkboxes:
            checkbox.setChecked(not all_selected)

    def calcStartAndEndTime(self):
        """Calculate the analysis start and end times based on user input."""
        hdr = self.selectedHeader
        start_datetime = datetime.strptime(hdr['startdate'] + " " + hdr['starttime'], "%y.%m.%d %H.%M.%S")
        session_duration = timedelta(seconds=hdr['records'] * hdr['duration'])
        end_datetime = start_datetime + session_duration

        if self.startendtimes_button.isChecked():
            # Get user-selected start and end times
            selected_start_time = datetime.strptime(f"{self.EditField_StartDate1.text()} {self.EditField_StartTime1.text()}", "%y.%m.%d %H:%M:%S")
            selected_end_time = datetime.strptime(f"{self.EditField_EndDate1.text()} {self.EditField_EndTime1.text()}", "%y.%m.%d %H:%M:%S")

            # Bound the selected times within the file's time range
            analysis_start_datetime = min(max(start_datetime, selected_start_time), end_datetime)
            analysis_end_datetime = max(min(end_datetime, selected_end_time), start_datetime)
            selected_duration = (analysis_end_datetime - analysis_start_datetime).total_seconds()

            # Calculate the analysis start and end in seconds from the start of the file
            analysis_start = max((selected_start_time - start_datetime).total_seconds(), 0)
            analysis_end = analysis_start + selected_duration
            returned_start_time = datetime.strptime(f"{self.EditField_StartDate1.text()} {self.EditField_StartTime1.text()}", "%d.%m.%y %H:%M:%S")

        elif self.starttimesec_button.isChecked():
            # Get user-selected start time and duration
            selected_start_time = datetime.strptime(f"{self.EditField_StartDate2.text()} {self.EditField_StartTime2.text()}", "%y.%m.%d %H:%M:%S")
            selected_duration = float(self.EditField_Sec.text())

            # Bound the start time and duration within the file's time range
            analysis_start_datetime = min(max(start_datetime, selected_start_time), end_datetime)
            analysis_end_datetime = analysis_start_datetime + timedelta(seconds=selected_duration)

            # Calculate the analysis start and end in seconds from the start of the file
            analysis_start = max((selected_start_time - start_datetime).total_seconds(), 0)
            analysis_end = analysis_start + (analysis_end_datetime - analysis_start_datetime).total_seconds()
            returned_start_time = datetime.strptime(f"{self.EditField_StartDate2.text()} {self.EditField_StartTime2.text()}", "%d.%m.%y %H:%M:%S")

        else:
            # If "Entire file" is selected
            analysis_start = 0
            analysis_end = -1  # Indicate no specific end time
            returned_start_time = datetime.strptime(hdr['startdate'] + " " + hdr['starttime'], "%d.%m.%y %H.%M.%S")

            selected_duration = session_duration
            

        # Ensure that the end time is after the start time
        if analysis_end != -1:
            analysis_end = max(analysis_end, analysis_start)

        # print(f"analysis_start_datetime: {analysis_start_datetime}, analysis_end_datetime: {analysis_end_datetime}")
        return analysis_start, analysis_end, returned_start_time


    def browse_files(self):
        # Determine the settings folder and default browse file path.
        settings_folder = self.get_settings_folder()
        # Create the folder if it doesn't exist.
        os.makedirs(settings_folder, exist_ok=True)
        default_browse_file = os.path.join(settings_folder, "default_browse.json")
        
        # Try to load the default search path from the JSON file.
        if os.path.exists(default_browse_file):
            try:
                with open(default_browse_file, "r") as f:
                    data = json.load(f)
                    default_search_path = data.get("defaultSearchPath", None)
                    if not default_search_path or not os.path.exists(default_search_path):
                        default_search_path = os.path.join(os.path.expanduser("~"), "Desktop")
            except Exception as e:
                default_search_path = os.path.join(os.path.expanduser("~"), "Desktop")
        else:
            default_search_path = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Open the folder browsing dialog with the default path.
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder", default_search_path)

        if folder_path:
            # Update the default search path in the JSON file.
            # os.makedirs(settings_folder, exist_ok=True)
            try:
                with open(default_browse_file, "w") as f:
                    json.dump({"defaultSearchPath": folder_path}, f, indent=4)
            except Exception as e:
                print("Error saving default browse path:", e)

            # Update the state and UI.
            self.state['defaultSearchPath'] = folder_path
            self.folder_label.setText(f"Folder path: {folder_path}")

            # Find all .edf files in the directory.
            file_pattern = os.path.join(folder_path, '**/*.edf')
            self.edfFiles = glob(file_pattern, recursive=True)

            # Create a progress dialog.
            progress_dialog = QProgressDialog("Preparing data...", None, 0, len(self.edfFiles), self)
            progress_dialog.setWindowTitle("Loading Data")
            progress_dialog.setWindowModality(True)  # Block interaction with other windows
            progress_dialog.setMinimumDuration(0)  # Immediately show the dialog
            progress_dialog.setValue(0)  # Start at 0%
            QApplication.processEvents()

            # Initialize data storage.
            data = []
            headers = {}
            edf_paths = []
            id_counter = 1

            # Process each EDF file.
            for edf_file in self.edfFiles:
                progress_dialog.setValue(id_counter)
                QApplication.processEvents()  # Keep the GUI responsive during the loop
                hdr = self.get_edf_info(edf_file)
                if hdr:
                    # Process header information.
                    session_duration = timedelta(seconds=hdr['records'] * hdr['duration'])
                    start_datetime = datetime.strptime(hdr['startdate'] + " " + hdr['starttime'], "%y.%m.%d %H.%M.%S")
                    end_datetime = start_datetime + session_duration
                    freq = f"{round(hdr['frequency'][0])}"
                    # Update headers and paths.
                    headers[id_counter] = hdr
                    edf_paths.append(edf_file)

                    # Add data to the table.
                    data.append([False, os.path.basename(edf_file), 
                                 start_datetime.strftime("%y.%m.%d"), 
                                 start_datetime.strftime("%H:%M:%S"), 
                                 end_datetime.strftime("%y.%m.%d"), 
                                 end_datetime.strftime("%H:%M:%S"),
                                 freq])
                    id_counter += 1
                progress_dialog.close()

            # Save headers and paths.
            self.edfHeaders = headers
            self.edfPaths = edf_paths

            # Update the available files table.
            self.update_available_files_table(data)


    def get_edf_info(self, edf_file):
        try:
            hdr = browse_edf(edf_file)
            return {
                'startdate': hdr['startdate'],
                'starttime': hdr['starttime'],
                'records': hdr['records'],
                'duration': hdr['duration'],
                'frequency': hdr['frequency'],
                'label': hdr['label']
            }
        except Exception as e:
            self.feedback.append(f"Error running get_edf_info on {edf_file}: {e}")
            return None

    def run_edfread(self, edf_file):
        try:
            [hdr, recorddata] = edfread(edf_file)
            return {
                'startdate': hdr['startdate'],
                'starttime': hdr['starttime'],
                'records': hdr['records'],
                'duration': hdr['duration'],
                'frequency': hdr['frequency'],
                'label': hdr['label']
            }
        except Exception as e:
            self.feedback.append(f"Error running edfread on {edf_file}: {e}")
            return None

    def update_available_files_table(self, data):
        """
        data is a list of lists where each inner list is:
          [selection col, file_name, start_date, start_time, end_date, end_time, freq]
        """

        # 8 columns: 7 visible plus 1 hidden sort column.
        self.available_files_table.setColumnCount(8)
        self.available_files_table.setRowCount(len(data))

        self.available_files_table.setHorizontalHeaderLabels(
            ["", "File Name", "Start Date", "Start Time", "End Date", "End Time", "Frequency", "Sort"]
        )

        for row_idx, row_data in enumerate(data):
            # Column 0: Checkbox
            checkbox = QCheckBox(self)
            checkbox.stateChanged.connect(lambda state, row=row_idx: self.on_file_selected(state, row))
            self.available_files_table.setCellWidget(row_idx, 0, checkbox)
            
            # For columns 1 to 6, add items from row_data.
            for col_idx in range(1, 7):
                item = QTableWidgetItem(str(row_data[col_idx]))
                if col_idx == 6:
                    try:
                        freq_value = float(row_data[col_idx])
                    except Exception:
                        freq_value = 0
                    item.setData(Qt.EditRole, freq_value)
                self.available_files_table.setItem(row_idx, col_idx, item)
            
            # Create a hidden sort column (column 7) by combining start date and start time.
            # row_data[2] holds start date (format: "%y.%m.%d") and row_data[3] holds start time.
            try:
                full_dt = datetime.strptime(row_data[2] + " " + row_data[3], "%d.%m.%y %H:%M:%S")
                sort_text = full_dt.strftime("%y-%m-%d %H:%M:%S")
            except Exception as e:
                # Fallback if date parsing fails.
                sort_text = row_data[2] + " " + row_data[3]
            
            sort_item = QTableWidgetItem(sort_text)
            # We want numerical (or lexicographical) comparison based on ISO formatted datetime.
            sort_item.setData(Qt.UserRole, full_dt.timestamp() if 'full_dt' in locals() else 0)
            self.available_files_table.setItem(row_idx, 7, sort_item)
        
        # Hide the sort column so it’s not visible to the user.
        self.available_files_table.setColumnHidden(7, True)

        # Disable built‐in sorting and connect header clicks:
        self.available_files_table.setSortingEnabled(False)



    def handle_section_clicked(self, index):
        # If this is the first time or a new column, default to ascending.
        if hasattr(self, 'last_sort_index') and self.last_sort_index == index:
            # Toggle the sort order if the same column is clicked.
            self.last_sort_order = (
                Qt.DescendingOrder if self.last_sort_order == Qt.AscendingOrder else Qt.AscendingOrder
            )
        else:
            self.last_sort_order = Qt.AscendingOrder
        
        # Store the last sorted column.
        self.last_sort_index = index

        # If the clicked column is Start Date (2) or Start Time (3), sort by the hidden sort column (7).
        if index == 2:
            self.available_files_table.sortItems(7, self.last_sort_order)
        # If Frequency (6) is clicked, sort by column 6.
        elif index == 6:
            self.available_files_table.sortItems(6, self.last_sort_order)
        else:
            # For other columns, you might do nothing or add custom behavior.
            pass

    def on_file_selected(self, state, row):

        if state == Qt.Checked:
            # Uncheck all other checkboxes
            for r in range(self.available_files_table.rowCount()):
                if r != row:
                    checkbox = self.available_files_table.cellWidget(r, 0)
                    checkbox.setChecked(False)

            # Set the selected EDF file and header
            self.selectedEdfFile = self.edfPaths[row]
            self.selectedHeader = self.edfHeaders[row + 1]  # Headers are stored by 1-based index
            self.load_state()
            # Dynamically categorize labels from the selected EDF file header
            self.categorize_labels(self.selectedHeader['label'])

            # Output to feedback or handle further processing
            self.feedback.append(f"Selected EDF File: {self.selectedEdfFile}")
            print(f"Selected EDF File: {self.selectedEdfFile}")
            # self.feedback.append(f"EDF Header: {self.selectedHeader}") #output header info to feedback window
            # print(f"selected file's frequency:{math.ceil(self.selectedHeader['frequency'][0])}")
            self.freq_thresh = math.ceil(self.selectedHeader['frequency'][0])


    def normalize_label(self, label):
        if label.startswith("DC"):
            # Ensure "DC" labels have two digits
            label = "DC" + str(int(label[2:]))  
        return label

    def custom_sort_key(self, label):
        # Extract the prefix and the number (if any) from the label
        match = re.match(r"([A-Za-z]+)(\d*)", label)
        if match:
            prefix, number = match.groups()
            # Convert the number to an integer if it exists, otherwise use a default value of 0
            number = int(number) if number else float('inf')  # Non-number labels will be sorted last
            return (prefix, number)
        return (label, float('inf'))  # If no match, return the original label and a high number for sorting


    def analyze(self):
        """Analyze function triggered by Analyze button."""

        if self.selectedEdfFile:
            try:

                # Notify user that the analysis has started
                self.feedback.append(f"Analyzing started on {self.selectedEdfFile}")
                
                # Get the file name from the selected path
                input_file_path = str(self.selectedEdfFile)
                
                # Calculate analysis start and end times
                analysis_start, analysis_end, self.returned_start_time = self.calcStartAndEndTime()

                # Check if the analysis start and end times are valid
                self.feedback.append(f"Analysis time: {analysis_start} to {analysis_end}")
                
                # Get montage and reference information
                montage, user_ref = self.calc_selected_montage()

                # Get selected labels for the leads
                labels = self.calc_selected_channel_labels(self.selectedHeader['label'])
                self.feedback.append(f"Selected leads: {labels}")
                
                # Notify user about data loading
                self.feedback.append("Loading your data...")
                
                header, recorddata = edfread(input_file_path)
                
                # Preprocess header labels: Remove prefixes and clean up label names
                for idx, label in enumerate(header['label']):
                    channel_id = re.search(r'\d+', label)  # Extract the channel ID (number)
                    channel_name = re.sub(r'^(POL )|(P )|(\d+)|( )', '', label)  # Clean the label
                    if channel_id and channel_name:
                        header['label'][idx] = f"{channel_name}{channel_id.group()}"
                    elif channel_name:
                        header['label'][idx] = channel_name


                # Ensure consistent label matching
                labels = [label.upper() for label in labels]  # Ensure case consistency
                header['label'] = [self.normalize_label(label.upper()) for label in header['label']]

                # Check which labels are available
                # self.feedback.append(f"Available header labels on file: {header['label']}")

                # Get indices of the selected leads from the header
                selected_indices = np.isin(header['label'], labels + ['REF'])  # Include 'REF' for reference if needed
                # self.feedback.append(f"selected_indices: {selected_indices}")
                # print(f"selected indices: {selected_indices}")
                # Debugging: Check the size of the header['frequency'] and selected_indices
                # self.feedback.append(f"header['frequency'] size: {len(header['frequency'])}")
                # self.feedback.append(f"selected_indices size: {len(selected_indices)}")
                
                # Handle the mismatch: if header['frequency'] is a scalar, broadcast it to match the number of labels
                if len(header['frequency']) == 1:
                    # Broadcast the single frequency value across all channels
                    header['frequency'] = np.full(len(header['label']), header['frequency'][0])
                
                # Check if the length of header['frequency'] matches header['label']
                if len(header['frequency']) != len(header['label']):
                    raise ValueError("Mismatch between the size of 'frequency' and 'label' in the header.")
                
                # Prepare EEG structure with selected data

                EEG = {
                    'chanlocs': np.array(header['label'])[selected_indices],
                    'data': np.array(recorddata)[selected_indices, :],
                    'nbchan': np.sum(selected_indices)
                }
                
                # Convert frequencies to integers for LCM calculation
                freqs = np.array(header['frequency'])[selected_indices]
                freqs = np.round(freqs).astype(int)  # Ensure frequencies are integers for LCM

                # print(freqs)
                # Calculate sampling rate (srate) from frequencies in the header
                if len(set(freqs)) == 1:
                    # If all frequencies are the same, use it directly as the sampling rate
                    srate = freqs[0]
                else:
                    # Use least common multiple (LCM) to calculate srate
                    srate = np.lcm.reduce(freqs)
                EEG['srate'] = srate
                header['srate'] = srate
                
                # Handle duplicate channel names
                unique_labels, unique_indices = np.unique(EEG['chanlocs'], return_index=True)
                if len(unique_labels) != len(EEG['chanlocs']):
                    # Notify about duplicates
                    duplicates = [label for label in EEG['chanlocs'] if list(EEG['chanlocs']).count(label) > 1]
                    for duplicate in duplicates:
                        self.feedback.append(f"There is more than one channel named {duplicate}")
                    
                    # Keep only unique channels
                    EEG['chanlocs'] = EEG['chanlocs'][unique_indices]
                    EEG['data'] = EEG['data'][unique_indices, :]
                    EEG['nbchan'] = len(unique_indices)
                    
                    # Recalculate srate with unique channels
                    freqs = np.array(header['frequency'])[unique_indices]
                    freqs = freqs.astype(int)  # Ensure frequencies are integers for LCM
                    if len(set(freqs)) == 1:
                        srate = freqs[0]
                    else:
                        srate = np.lcm.reduce(freqs)
                    EEG['srate'] = srate
                    header['srate'] = srate
                
                # Get the cutoff frequencies for the analysis
                locutoff, hicutoff = self.get_cutoff_freqs(self.freq_thresh)
                self.feedback.append(f"Cutoff frequencies: {locutoff} Hz to {hicutoff} Hz")
                
                self.feedback.append(f"patient ID: {header['patientID']}")
                self.feedback.append("Processing data...")
                self.threshold_options = self.get_selected_threshold_values()
                try:
                    result = run_hfo_algorithm(EEG, input_file_path, analysis_start, analysis_end, montage, user_ref,
                                            locutoff, hicutoff, lambda msg: self.to_feedback(msg), self.threshold_options[0],
                                             self.threshold_options[1], self.threshold_options[2], self.threshold_options[3], self.threshold_options[4], self.threshold_options[5], self.threshold_options[6])

                    if result['success']:

                        # Get the base path
                        output_dir = self.get_reports_folder()  # hifod >> report
                        print(f"output_dir: {output_dir}")

                        # Check if the directory exists; if not, create it
                        if os.path.exists(output_dir):
                            self.feedback.append(f"Directory '{output_dir}' already exists.")
                        else:
                            os.makedirs(output_dir, exist_ok=True)  # Use output_dir directly
                            self.feedback.append(f"Directory '{output_dir}' created.")
                            
                        file_date = header.get('startdate', 'default_date')
                        
                        # Update the feedback message to inform the user of the next steps
                        self.feedback.append("Plotting and saving the results...")
                        
                        time.sleep(0.1)
                        
                        # Pass all necessary data to the 'plotAndSaveHfo' function
                        variables = plotAndSaveHfo(output_dir, result, file_date, lambda msg: self.to_feedback(msg), header, locutoff, hicutoff, input_file_path,os.path.splitext(os.path.basename(self.selectedEdfFile))[0])
                        self.plotter = EEGPlotterWindow(*variables, header['patientID'],self.returned_start_time,self.threshold_options[0])
                        self.plotter.show()
                        # Update feedback message to show analysis is finalizing
                        self.feedback.append("Finalizing the analysis...")
                        
                        # Pause again to simulate finalization
                        time.sleep(0.1)
                        
                        # Final message confirming completion
                        self.feedback.append("Analysis complete.\n")
                        self.feedback.append("-----------------------------End of Analysis-----------------------------")

                    else:
                        self.feedback.append("Analysis failed.")

                except ValueError as e:
                    if "not enough values to unpack" or "operation lcm which has no identity" in str(e):
                        self.feedback.append("Please select at least one pair of contacts before pressing \"Analyze\".")
                    elif "Digital filter critical frequencies" in str(e):
                        self.feedback.append(f"Please select appropriate cutoff filters. High cutoff must below 1/3 of the sampling rate of the data({srate})")
                    elif "The length of the input vector x must be greater than padlen" in str(e):                      
                        self.feedback.append(f"Please check ths start/end time. The end time must be later than the start time!")
                    else:
                        self.feedback.append(f"Program Error: {e}")
            except Exception as e:
                if "calcStartAndEndTime" in traceback.format_exc():
                    self.feedback.append("Please specify the Start date (dd.mm.yy) , Start Time (hh.mm.ss) and the number of Seconds (integer) in the correct format before pressing \"Analyze\".")
                elif "srate = np.lcm.reduce(freqs)" in traceback.format_exc() and len(labels) < 2:
                    self.feedback.append("Please select at least one pair of contacts before pressing \"Analyze\".")
                elif "operation lcm which has no identity" in str(e):
                    self.feedback.append("Please select at least one pair of contacts before pressing \"Analyze\".")
                else:
                    self.feedback.append(f"An error occurred: {traceback.format_exc()}")
                # Capture and print the complete traceback
                # self.feedback.append(f"ERROR TRACEBACK:\n{traceback.format_exc()}")
            
        else:
            # Notify if no EDF file is selected
            self.feedback.append("One EDF file must be selected.")
    

    def to_feedback(self, input):
        """Updates the feedback section with the provided message."""
        self.feedback.append(input)  # Update the feedback section in the GUI


    def cancel(self):
        self.feedback.clear()
        for edit in self.lead_edit_fields:
            edit.clear()

    def closeEvent(self, event):
        """Override closeEvent to save state before the app closes."""
        reply = QMessageBox.question(
            self, "Close Application", "Are you sure you want to close the application?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

class MplCanvas(FigureCanvas):
    """Matplotlib canvas class to embed figures in PyQt5."""
    def __init__(self, parent=None, width=12, height=8, dpi=100):
        fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = fig.add_subplot(111)
        super().__init__(fig)


class EEGPlotterWindow(QMainWindow):
    def __init__(self, hfo_ch_t, Schan, Echan, num_pts, myEEG, final_start_ind, 
                 final_end_ind, lfo_start_ind, lfo_end_ind, rejected_start_ind, 
                 rejected_end_ind, rejecLONG_start_ind, rejecLONG_end_ind, 
                 channel_labels, counter, num_min, 
                 samp_freq, analysis_epoch, 
                 RMS2, meanHilbert, stdHilbert, win_len2, input_file_path, report_file_path, report_filename,locutoff,hicutoff,patientID, returned_start_time,thresh):
        super().__init__()
        
        self.setWindowIcon(QIcon(resource_path("biormika_icon.ico")))

        # Set the window title to the full file path
        self.report_file_path = report_file_path
        self.full_path = input_file_path
        self.setWindowTitle(self.full_path)
        self.file_name = report_filename
        # Initialize variables
        self.schan = Schan
        self.echan = Echan
        self.num_pts = num_pts
        self.myEEG = myEEG
        self.final_start_ind = final_start_ind
        self.final_end_ind = final_end_ind
        self.rejected_start_ind = rejected_start_ind
        self.rejected_end_ind = rejected_end_ind
        self.samp_freq = samp_freq
        self.RMS2 = RMS2
        self.meanHilbert = meanHilbert
        self.stdHilbert = stdHilbert
        self.win_len2 = win_len2
        self.thresh = thresh
        self.display_start = 0  # Default display start at 0 seconds
        self.display_end = 10  # Default display span of 10 seconds
        self.display_segment = self.display_end - self.display_start
        self.y_factor = 100
        self.display_flag = True  # Toggle for automated HFO display
        self.labels_visibility = {i: True for i in range(Schan, Echan + 1)}
        self.annotation_labels_to_draw = list(range(Schan, Echan + 1))
        self.threshold_visible = False  # Control visibility of threshold lines
        self.recandhilbert_visible = False  # Control visibility of rectified EEG (green) and Hilbert (gray)
        self.locutoff = locutoff
        self.hicutoff = hicutoff
        self.patientID = patientID
        self.returned_start_time = returned_start_time


        self.predefined_timebases = [1, 2, 3, 4, 5, 10]  # List of predefined timebase settings
        self.current_timebase_index = self.predefined_timebases.index(5)  # Start at 5 seconds as an example


        # Set up the user interface
        self.initUI(channel_labels)

    def initUI(self, channel_labels):
        # Use QSplitter to divide left and right panels with fixed ratio
        splitter = QSplitter(Qt.Horizontal)
        splitter.setSizes([1, 5])  # 1:5 ratio between left and right panels

        # Left panel for channel checkboxes
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_widget.setStyleSheet("font-size: 14px;")

        # menu bar
        menuBar = self.menuBar()

        # Add File menu
        fileMenu = menuBar.addMenu('File')
        aboutMenu = menuBar.addMenu('About')

        # action add to the About menu
        aboutAction = QAction('Information', self)
        aboutAction.triggered.connect(self.show_about_dialog)
        aboutMenu.addAction(aboutAction)

        # Create actions for the File menu
        loadSettingAction = QAction('Load display settings', self)
        saveSettingAction = QAction('Save display settings', self)
        GenReportWithFigAction =QAction('Generate report with graphics', self)
        NoFigReportAction =QAction('Generate report without graphics', self)
        savePlotAction = QAction('Export graphic', self)

        GenReportWithFigAction.triggered.connect(lambda: self.generate_pdf_report(with_plot=True))
        NoFigReportAction.triggered.connect(lambda: self.generate_pdf_report(with_plot=False))
        savePlotAction.triggered.connect(self.export_plot)
        loadSettingAction.triggered.connect(self.load_display_settings)
        saveSettingAction.triggered.connect(self.save_display_settings)


        # Add actions to the File menu
        fileMenu.addAction(loadSettingAction)
        fileMenu.addAction(saveSettingAction)
        fileMenu.addAction(GenReportWithFigAction)
        fileMenu.addAction(NoFigReportAction)
        fileMenu.addAction(savePlotAction)

        self.checkboxes = []
        self.channel_names = [f"{i}: {label}" for i, label in enumerate(channel_labels[self.schan-1:self.echan], start=self.schan)]
        for name in self.channel_names:
            checkbox = QCheckBox(name)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_plot)
            scroll_layout.addWidget(checkbox)
            self.checkboxes.append(checkbox)

        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        left_layout.addWidget(scroll_area)

        # Right panel for the plot
        plot_panel = QWidget()
        plot_layout = QVBoxLayout(plot_panel)


        self.canvas = MplCanvas(self, width=10, height=8, dpi=100)

        
        plot_layout.addWidget(self.canvas)

        # Add panels to the splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(plot_panel)
        splitter.setSizes([100, 800])  # Initial size for left and right panels

        # Set splitter as the central widget
        self.setCentralWidget(splitter)

        self.show()
        self.draw_data()

    def show_about_dialog(self):
        """Show the About dialog box."""
        about_box = QMessageBox(self)
        about_box.setIcon(QMessageBox.Information)
        about_box.setWindowTitle("About")
        about_box.setTextFormat(Qt.RichText)
        about_box.setText(
            "<div style='text-align: center;'>"
            "About<br><br>"
            "App Name: <b>biormika Detector</b><br><br>"
            "Version 2.0 Build 202501a<br><br>"
            "App Designed by: Pradeep Modur, MD, MS<br>"
            "App Programmed by: Ju-Chun (Leon) Hsieh<br>"
            "Austin, TX, USA<br><br>"
            "All rights reserved"
            "</div>"
            )
        about_box.setStandardButtons(QMessageBox.Ok)
        about_box.exec_()

    def update_plot(self):
        """Update the plot based on checkbox selection."""
        for i, checkbox in enumerate(self.checkboxes):
            self.labels_visibility[self.schan + i] = checkbox.isChecked()
        self.draw_data()

    def keyPressEvent(self, event):
        """Handle key press events."""
        key = event.key()
        modifiers = event.modifiers()
        
        if modifiers == Qt.ControlModifier and key == Qt.Key_E:  # Fit plot to display segment
            self.canvas.axes.set_xlim([self.display_start, self.display_end])
            self.draw_data()
        elif modifiers == Qt.ControlModifier and key == Qt.Key_R:  # Reset plot
            self.reset_plot()
        elif modifiers == Qt.ControlModifier and key == Qt.Key_Z:  # Toggle automated HFO display
            self.display_flag = not self.display_flag
            self.draw_data()
        elif modifiers == Qt.ControlModifier and key == Qt.Key_T:  # Toggle threshold lines
            self.threshold_visible = not self.threshold_visible
            self.draw_data()
        elif modifiers == Qt.ControlModifier and key == Qt.Key_H:  # Toggle Rec and hilbert
            self.recandhilbert_visible = not self.recandhilbert_visible
            self.draw_data()            
        elif modifiers == Qt.ControlModifier and key == Qt.Key_F:  # Toggle fullscreen mode
            if self.isFullScreen():
                self.showNormal()  # Exit fullscreen mode
            else:
                self.showFullScreen()  # Enter fullscreen mode

        elif key == Qt.Key_A:  # Decrease timebase (zoom in on time axis)
            if self.current_timebase_index > 0:  # Ensure not below minimum setting
                self.current_timebase_index -= 1
                self.display_segment = self.predefined_timebases[self.current_timebase_index]
                self.adjust_timebase()
        elif key == Qt.Key_D:  # Increase timebase (zoom out on time axis)
            if self.current_timebase_index < len(self.predefined_timebases) - 1:  # Ensure not above maximum setting
                self.current_timebase_index += 1
                self.display_segment = self.predefined_timebases[self.current_timebase_index]
                self.adjust_timebase()

        elif key == Qt.Key_W:  # Zoom in on amplitude
            self.y_factor /= 2
            self.draw_data()
        elif key == Qt.Key_S:  # Zoom out on amplitude
            self.y_factor *= 2
            self.draw_data()
        elif key == Qt.Key_O:  # Scroll left
            self.scroll_display(-1)
        elif key == Qt.Key_P:  # Scroll right
            self.scroll_display(1)

    def adjust_timebase(self):
        """Adjust the display to the new timebase."""
        self.display_start = max(0, self.display_start)  # Ensure display start is non-negative
        self.display_end = self.display_start + self.display_segment
        self.draw_data()

    def scroll_display(self, direction):
        """Scroll the display left or right."""
        step = self.display_segment  # Move by one page, i.e., the current timebase
        new_start = self.display_start + direction * step
        new_end = new_start + self.display_segment

        # Ensure new start and end are within bounds
        self.display_start = max(0, min(new_start, (self.num_pts / self.samp_freq) - self.display_segment))
        self.display_end = self.display_start + self.display_segment
        self.draw_data()

    def zoom_display(self, factor):
        """Zoom in or out on the time axis."""
        center = (self.display_start + self.display_end) / 2
        new_segment = self.display_segment * factor
        min_segment = 1 / self.samp_freq
        max_segment = self.num_pts / self.samp_freq

        self.display_segment = max(min_segment, min(new_segment, max_segment))
        self.display_start = max(0, center - self.display_segment / 2)
        self.display_end = min(self.num_pts / self.samp_freq, self.display_start + self.display_segment)
        self.draw_data()

    def reset_plot(self):
        """Reset the plot to the default view."""
        self.display_start = 0
        self.display_end = 10
        self.y_factor = 100
        self.display_flag = False
        self.draw_data()

    def draw_data(self):
        """Draw the EEG data on the canvas."""
        self.canvas.axes.clear()
        ct3 = 0  # Counter for plotted channels
        y_ticks = []  # Store tick positions for each channel
        y_labels = []  # Store channel labels for each tick

        channel_spacing = 100  # Fixed spacing between channels
        amplitude_scale = 100 / self.y_factor  # Scale factor for signal amplitude only
        duration = self.num_pts / self.samp_freq

        info_title = (
            f"Patient Info: {self.patientID}  | "
            f"Sampling Rate: {self.samp_freq} Hz | "   
            f"Frequency Band: {self.locutoff}-{self.hicutoff} Hz | "
            f"Start Date: {self.returned_start_time.date()} | "
            f"Start time: {self.returned_start_time.time()} | "            
            f"Duration: {duration:.2f} seconds | "
            # f"Display Range: {int(self.display_start)} s - {int(self.display_end)} s"
        )

        max_y_value = 0  # Track the max y-value for setting plot limits dynamically
        k = 1
        m = 1
        for idx, i in enumerate(self.annotation_labels_to_draw):
            if self.labels_visibility[i]:
                ct3 += 1
                time = np.arange(self.num_pts) / self.samp_freq

                # Apply amplitude scaling only to the signal
                eeg_data = self.myEEG[i - 1, :] * amplitude_scale
                hilbert_data = self.RMS2[i - 1, :self.num_pts - (self.win_len2 - 1)] * amplitude_scale

                # Use a fixed y-offset for each channel to keep spacing constant
                y_offset = -(ct3 - 1) * channel_spacing

                # Plot EEG, Rectified EEG, and Hilbert Transform
                self.canvas.axes.plot(time, eeg_data + y_offset, 'k-', label='EEG' if idx == 0 else "")
                if self.recandhilbert_visible:
                    self.canvas.axes.plot(time, np.abs(eeg_data) + y_offset, 'g--', label='Rectified EEG' if idx == 0 else "")
                    self.canvas.axes.plot(time[:len(hilbert_data)], hilbert_data + y_offset, 'gray', label='Energy Signal' if idx == 0 else "")

                if self.threshold_visible:
                    # Plot horizontal lines for mean Hilbert and threshold
                    self.canvas.axes.axhline(self.meanHilbert[i - 1]* amplitude_scale + y_offset, color='cyan', linestyle='--', label='Mean Energy Signal' if idx == 0 else "")
                    self.canvas.axes.axhline(
                        (self.meanHilbert[i - 1] + self.thresh * self.stdHilbert[i - 1]) * amplitude_scale + y_offset,
                        color='magenta', linestyle='--', label='Threshold' if idx == 0 else ""
                    )

                # Plot automated HFO selections if display_flag is enabled
                if self.display_flag:
                    for start, end in zip(self.final_start_ind[i - 1], self.final_end_ind[i - 1]):
                        start = int(start)
                        end = int(end)  
                        if time[start:end].size > 0:
                            self.canvas.axes.plot(time[start:end], eeg_data[start:end] + y_offset, 'r-', label='True HFOs' if k == 1 else None)
                            k = 0
                    for start, end in zip(self.rejected_start_ind[i - 1], self.rejected_end_ind[i - 1]):
                        start = int(start)
                        end = int(end)
                        if time[start:end].size > 0:
                            self.canvas.axes.plot(time[start:end], eeg_data[start:end] + y_offset, 'b-', label='Rejected HFOs' if m == 1 else None)
                            m = 0



                # Collect y-tick information for each channel
                y_ticks.append(y_offset)
                y_labels.append(self.channel_names[i - self.schan])

                # Track the maximum y-value for setting dynamic y-limits
                max_y_value = max(max_y_value, abs(y_offset) + amplitude_scale * np.max(np.abs(eeg_data)))

        # Set y-ticks and labels
        self.canvas.axes.set_yticks(y_ticks)
        self.canvas.axes.set_yticklabels(y_labels)

        # Dynamically adjust the y-limits of the plot
        self.canvas.axes.set_ylim([-ct3 * channel_spacing, channel_spacing])

        # Adjust the plot layout and legend
        self.canvas.figure.subplots_adjust(right=0.75)
        self.canvas.axes.legend(
        loc='center left',
        bbox_to_anchor=(1, 0.5),
        borderaxespad=0,
        frameon=True
        )

        # Set x-axis limits and labels
        self.canvas.axes.set_xlim([self.display_start, self.display_end])
        self.canvas.axes.set_xlabel('Time (sec)')

        # Apply tight layout to prevent overlaps
        self.canvas.figure.tight_layout()
        self.canvas.figure.subplots_adjust(top=0.96)
        # Get the renderer
        renderer = self.canvas.figure.canvas.get_renderer()

        # Get the left-most x coordinate among the y-axis tick labels
        tick_labels = self.canvas.axes.get_yticklabels()
        if tick_labels:
            left_extent = min([label.get_window_extent(renderer).x0 for label in tick_labels])
            # Transform the display coordinate into axes coordinate
            inv = self.canvas.axes.transAxes.inverted()
            left_in_axes = inv.transform((left_extent, 0))[0]
        else:
            left_in_axes = 0.0  # Fallback if no tick labels

        # Set the title with the computed x coordinate
        self.canvas.axes.set_title(info_title, fontsize=10, loc='left', x=left_in_axes,y=1.0)


        # Draw the updated plot
        self.canvas.draw()

    def get_settings_folder(self):
        # If running as an executable, save to Desktop/hifod/settings
        if getattr(sys, 'frozen', False):
            return os.path.join(os.path.expanduser("~"), "Desktop", "hifod", "settings")
        else:
            # Otherwise, save settings in a subfolder relative to the script
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), "hifod", "settings")

    def save_display_settings(self):

        displayed_start_dt = self.returned_start_time + timedelta(seconds=self.display_start)

        settings = {
            "displayed_start_date": displayed_start_dt.strftime("%d.%m.%y"),
            "displayed_start_time": displayed_start_dt.strftime("%H:%M:%S"),
            "timebase": self.display_segment,   # Number of seconds on the x-axis.
            "gain": self.y_factor,              # y-axis scaling factor.
            # Save only the channel labels that are checked.
            "channels": [channel for checkbox, channel in zip(self.checkboxes, self.channel_names) if checkbox.isChecked()]
        }

        self.settings_folder = self.get_settings_folder()
        # print(f"\n----------------------graphic settings_folder path: {self.settings_folder} \n")
        # self.settings_path = self.base_path + f"/hifod/settings/{file_name}.json"

        self.base_filename = os.path.splitext(os.path.basename(self.full_path))[0]
        self.settings_path = os.path.join(self.settings_folder, f"{self.base_filename}_graphic_setting.json")
        # print(f"graphic setting path:{self.settings_path}")

        try:
            with open(self.settings_path, "w") as f:
                json.dump(settings, f, indent=4)
            QMessageBox.information(self, "Display Settings Saved",
                                    f"Display settings saved to:\n{self.settings_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error Saving Settings",
                                 f"An error occurred while saving display settings:\n{e}")

    def load_display_settings(self):

        self.settings_folder = self.get_settings_folder()
    
        # self.settings_path = self.base_path + f"/hifod/settings/{file_name}.json"
        
        self.base_filename = os.path.splitext(os.path.basename(self.full_path))[0]
        self.settings_path = os.path.join(self.settings_folder, f"{self.base_filename}_graphic_setting.json")
        # print(f"graphic setting path:{self.settings_path}")
        
        if not os.path.exists(self.settings_path):
            QMessageBox.information(self, "No Display Settings",
                                    "No saved display settings exist for this file.")
            return

        try:
            with open(self.settings_path, "r") as f:
                settings = json.load(f)
        except Exception as e:
            QMessageBox.critical(self, "Error Loading Settings",
                                 f"An error occurred while loading display settings:\n{e}")
            return

        try:
            # Combine saved date and time to form the displayed start datetime.
            displayed_start_str = settings["displayed_start_date"] + " " + settings["displayed_start_time"]
            loaded_displayed_start = datetime.strptime(displayed_start_str, "%d.%m.%y %H:%M:%S")
            # print(f"loaded_displayed_start: {loaded_displayed_start}")
            # print(f"returned_start_time: {self.returned_start_time}")
            # Compute the offset (in seconds) relative to the file's start.
            offset_seconds = (loaded_displayed_start - self.returned_start_time).total_seconds()
            # print(f"offset seconds: {offset_seconds}")
            self.display_start = max(0, offset_seconds)
        except Exception as e:
            QMessageBox.warning(self, "Error Parsing Start Time",
                                  f"Error parsing saved start date/time:\n{e}")

        try:
            self.display_segment = float(settings["timebase"])
            self.y_factor = float(settings["gain"])
            self.display_end = self.display_start + self.display_segment  
        except Exception as e:
            QMessageBox.warning(self, "Error Parsing Timebase/Gain",
                                  f"Error parsing saved timebase or gain:\n{e}")

        # Update the channel checkboxes based on the saved channel labels.
        saved_channels = settings.get("channels", [])
        for checkbox, channel in zip(self.checkboxes, self.channel_names):
            # Check the box if the channel label is in the saved list.
            checkbox.setChecked(channel in saved_channels)

        self.draw_data()
        QMessageBox.information(self, "Display Settings Loaded",
                                "Display settings loaded successfully.")


    def generate_pdf_report(self, with_plot=True):
        """Generate a PDF report containing the CSV data and optionally the current plot."""
        # Create a PDF object
        pdf = FPDF()
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()
        pdf.set_font("Arial", size=12)

        # Add a title
        pdf.set_font("Arial", "B", size=16)
        pdf.cell(0, 10, self.file_name, ln=True, align="C")
        pdf.ln(10)

        # Add CSV data
        pdf.set_font("Arial", "B", size=12)
        pdf.cell(0, 10, "CSV Data:", ln=True)
        pdf.ln(5)
        pdf.set_font("Arial", size=10)

        # Read the CSV file and write to PDF
        try:
            with open(self.report_file_path, "r") as csv_file:
                for line in csv_file:
                    pdf.multi_cell(0, 10, line.strip())
        except Exception as e:
            pdf.cell(0, 10, f"Error reading CSV file: {e}", ln=True)

        # Add plot only if with_plot is True
        if with_plot:
            # Save the current plot to a temporary image file
            plot_image_path = os.path.join(os.path.dirname(self.report_file_path), "current_plot.png")
            self.canvas.figure.tight_layout()  # Ensure proper layout
            self.canvas.figure.savefig(plot_image_path, dpi=200, bbox_inches="tight")

            # Determine image dimensions
            from PIL import Image
            with Image.open(plot_image_path) as img:
                img_width, img_height = img.size

            # Decide page orientation (landscape or portrait) based on the image's aspect ratio
            if img_width > img_height:
                pdf.add_page(orientation="L")  # Landscape
            else:
                pdf.add_page()  # Default (portrait)

            # Adjust dimensions to fit the image on the page
            margin = 10  # Margin size
            pdf_width = pdf.w - 2 * margin
            pdf_height = pdf.h - 2 * margin

            # Add the image to the PDF
            pdf.set_font("Arial", "B", size=12)
            pdf.image(plot_image_path, x=margin, y=pdf.get_y(), w=pdf_width, h=pdf_height)

            # Clean up the temporary plot image
            if os.path.exists(plot_image_path):
                os.remove(plot_image_path)

        # Save the PDF to the output directory
        suffix = "_with_graphics" if with_plot else "_no_graphics"
        pdf_report_path = os.path.join(os.path.dirname(self.report_file_path), f"{os.path.splitext(self.file_name)[0]}{suffix}.pdf")
        pdf.output(pdf_report_path)

        # Inform the user
        QMessageBox.information(self, "Report Generated", f"PDF report saved to:\n{pdf_report_path}")


    def export_plot(self):
        """Export the current plot as a high-resolution PNG to the report folder."""
        # Determine the output folder from the report file path.
        output_folder = os.path.dirname(self.report_file_path)
        # Create a filename for the exported plot.
        base_name = os.path.splitext(self.file_name)[0].strip("report_")
        export_filename = f"Graphics_{base_name}.png"
        export_path = os.path.join(output_folder, export_filename)
        
        # Save the current plot with high resolution (e.g., 300 dpi)
        self.canvas.figure.tight_layout()
        self.canvas.figure.savefig(export_path, dpi=300, bbox_inches="tight")
        
        # Inform the user that the plot has been exported
        QMessageBox.information(self, "Graphic Exported", f"Plot exported to:\n{export_path}")

    def closeEvent(self, event):
        """Ask the user to confirm before closing the window."""
        confirm_box = QMessageBox(self)
        confirm_box.setIcon(QMessageBox.Question)
        confirm_box.setWindowTitle("Confirm Exit")
        confirm_box.setText("Are you sure you want to close the application?")
        confirm_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        confirm_box.setDefaultButton(QMessageBox.No)
        
        # Execute the dialog and get the response
        response = confirm_box.exec_()
        if response == QMessageBox.Yes:
            event.accept()  # Allow the window to close
        else:
            event.ignore()  # Ignore the close event, keeping the window open

# Main function to run the application
def main():
    app = QApplication(sys.argv)
    main_window = HfoDetectionApp()
    main_window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
