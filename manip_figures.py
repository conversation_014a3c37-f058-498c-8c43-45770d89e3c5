import sys
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QCheckBox, QScrollArea
)
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

class EEGPlotterWindow(QMainWindow):
    def __init__(self, hfo_ch_t, <PERSON><PERSON>, <PERSON><PERSON>, num_pts, myEEG, final_start_ind, final_end_ind,
                 lfo_start_ind, lfo_end_ind, rejected_start_ind, rejected_end_ind,
                 rejecLONG_start_ind, rejecLONG_end_ind, channel_labels, counter, num_min,
                 display_start, display_end, samp_freq, user_analysis_time, input_dir,
                 analysis_epoch, analysis_start, RMS2, meanHilbert, stdHilbert, win_len2, thresh):
        super().__init__()

        # Store parameters
        self.hfo_ch_t = hfo_ch_t
        self.<PERSON>han = Schan
        self.Echan = Echan
        self.num_pts = num_pts
        self.myEEG = myEEG
        self.final_start_ind = final_start_ind
        self.final_end_ind = final_end_ind
        self.lfo_start_ind = lfo_start_ind
        self.lfo_end_ind = lfo_end_ind
        self.rejected_start_ind = rejected_start_ind
        self.rejected_end_ind = rejected_end_ind
        self.channel_labels = channel_labels
        self.RMS2 = RMS2
        self.meanHilbert = meanHilbert
        self.stdHilbert = stdHilbert
        self.win_len2 = win_len2
        self.thresh = thresh
        self.samp_freq = samp_freq
        self.display_start = 0
        self.display_end = 5
        self.y_factor = 100
        self.display_flag = False
        self.display_flag2 = False
        self.annotation_labels_to_draw = list(range(Schan, Echan + 1))
        self.labels_visibility = {i: True for i in range(Schan, Echan + 1)}

        self.initUI()

    def initUI(self):
        """Initialize the UI components."""
        main_widget = QWidget(self)
        layout = QVBoxLayout(main_widget)

        # Left Panel with Checkboxes (Scrollable)
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Add checkboxes for each channel
        self.checkboxes = []
        for i, label in enumerate(self.channel_labels[self.Schan - 1:self.Echan], start=self.Schan):
            checkbox = QCheckBox(f"{i}: {label}")
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_plot)
            scroll_layout.addWidget(checkbox)
            self.checkboxes.append(checkbox)

        scroll.setWidget(scroll_content)

        # Plot Area using Matplotlib FigureCanvas
        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Add widgets to layout
        layout.addWidget(scroll)
        layout.addWidget(self.canvas)
        self.setCentralWidget(main_widget)

        self.setWindowTitle("EEG Plotter")
        self.canvas.mpl_connect('key_press_event', self.keypress)

        # Initial plot
        self.draw_data()

    def update_plot(self):
        """Update the plot based on checkbox states."""
        for i, checkbox in enumerate(self.checkboxes):
            self.labels_visibility[i + self.Schan - 1] = checkbox.isChecked()
        self.draw_data()

    def keypress(self, event):
        """Handle keypress events."""
        if event.key == 'o':
            self.scroll_display(-1)
        elif event.key == 'p':
            self.scroll_display(1)
        elif event.key == 'a':
            self.zoom_display(1 / 1.5)
        elif event.key == 'd':
            self.zoom_display(1.5)
        elif event.key == 'z':
            self.display_flag = not self.display_flag
            self.draw_data()
        elif event.key == 'r':
            self.reset_plot()

    def scroll_display(self, direction):
        """Scroll the display left or right."""
        step = (self.display_end - self.display_start) / 4
        self.display_start = max(0, self.display_start + direction * step)
        self.display_end = self.display_start + 5
        self.draw_data()

    def zoom_display(self, factor):
        """Zoom in/out on the time axis."""
        center = (self.display_start + self.display_end) / 2
        segment = (self.display_end - self.display_start) * factor
        self.display_start = max(0, center - segment / 2)
        self.display_end = self.display_start + segment
        self.draw_data()

    def reset_plot(self):
        """Reset plot to default values."""
        self.display_start = 0
        self.display_end = 5
        self.y_factor = 100
        self.display_flag = False
        self.display_flag2 = False
        self.draw_data()

    def draw_data(self):
        """Plot the EEG data."""
        self.ax.clear()
        ct3 = 0  # Counter for channels

        time = np.arange(self.num_pts) / self.samp_freq

        for i in self.annotation_labels_to_draw:
            if self.labels_visibility[i]:
                ct3 += 1
                eeg_data = self.myEEG[i - 1, :]
                self.ax.plot(time, eeg_data - (ct3 - 1) * self.y_factor, label=f'Channel {i}')

        # Set plot limits and labels
        self.ax.set_xlim([self.display_start, self.display_end])
        self.ax.set_xlabel('Time (sec)')
        self.ax.legend(loc='upper right')
        self.canvas.draw()