
import numpy as np
import os

def find_blanks(myEEG, orig_channel_labels):
    """Find discontinuities or blank periods in EEG signal and identify artifact channels."""
    orig_num_channels, orig_num_pts = myEEG.shape

    BS = []  # List of blank_start vectors for all channels
    BE = []  # List of blank_end vectors for all channels

    for j in range(orig_num_channels):
        data = myEEG[j, :]

        flag = 0
        blank_start = []
        blank_end = []
        ct = 0

        # Find areas where there are 3 identical data points in a row
        for i in range(orig_num_pts - 2):
            if data[i] == data[i + 1] == data[i + 2]:
                if flag == 0:
                    flag = 1
                    blank_start.append(i)
            else:
                if flag == 1:
                    flag = 0
                    blank_end.append(i + 1)
                    ct += 1

        if flag == 1:
            blank_end.append(orig_num_pts)

        # Eliminate all blanks that are less than 20 points (likely real data)
        i = 0
        while i < len(blank_start):
            if blank_end[i] - blank_start[i] + 1 < 20:
                del blank_start[i]
                del blank_end[i]
            else:
                i += 1

        BS.append(blank_start)
        BE.append(blank_end)

    num_blanks = []
    for j in range(orig_num_channels):
        num_blanks.append(len(BS[j]))

    true_num_blanks = int(np.median(num_blanks)) if num_blanks else 0

    artifact_channels = []
    for j in range(orig_num_channels):
        if num_blanks[j] != true_num_blanks:
            artifact_channels.append(orig_channel_labels[j])

    true_blank_start = []
    true_blank_end = []
    for j in range(orig_num_channels):
        if num_blanks[j] == true_num_blanks:
            true_blank_start = BS[j]
            true_blank_end = BE[j]
            break

    return true_blank_start, true_blank_end, artifact_channels
