import numpy as np
from scipy.signal import find_peaks, spectrogram
from numpy import hamming
from scipy.fft import fft

def calculate_HFO_characteristics(data, s_freq, noise_freq_removal, noise_freq_vector, hfo_freq, max_freq, pwr_thresh):
    """
    Returns characteristics of the given HFO.
    
    Args:
    - data: The EEG segment data representing the HFO.
    - s_freq: Sampling frequency.
    - noise_freq_removal: Set to 1 or 0 for noise removal.
    - noise_freq_vector: Frequencies to be removed if too close to noise frequencies.
    - hfo_freq: The threshold frequency for HFO.
    - max_freq: The maximum frequency to consider.
    - pwr_thresh: The power threshold for HFO detection.

    Returns:
    - flag: 1 if true HFO, 0 if too low frequency, 2 if too close to noise.
    - duration: Duration of HFO in milliseconds.
    - peak_power: Peak power from FFT.
    - total_power_hfo: Power in the HFO frequency range from FFT.
    - peak_freq: Peak frequency from FFT.
    - my_amp: Mean amplitude of the rectified signal.
    - max_freq: Maximum significant frequency in FFT.
    """
    
    MAX_FREQ = max_freq
    HFO_FREQ = hfo_freq
    peak_to_peak_freq = 1
    max_freq_thresh = 0.01  # Minimum fraction of peak power for frequency significance

    flag = 0  # Default to not a true HFO
    duration = (len(data) / s_freq) * 1000  # Duration in ms
    my_amp = np.mean(np.abs(data))  # Mean amplitude

    # Peak-to-peak frequency calculation
    if peak_to_peak_freq == 1:
        peaks, _ = find_peaks(data)
        if len(peaks) > 1:
            pptime = np.diff(peaks) / s_freq  # Peak-to-peak time in seconds
            peak_freq1 = 1 / np.mean(pptime)
            max_freq1 = 1 / np.min(pptime)
        else:
            flag = 0  # Not enough peaks for peak-to-peak frequency
            peak_freq1, max_freq1 = 0, 0
    else:
        peak_freq1, max_freq1 = 0, 0

    # FFT parameters
    Npts = len(data)
    FFT_length = 512
    NFFT = max(2 ** int(np.ceil(np.log2(Npts))), FFT_length)
    NptsF = (NFFT // 2) + 1  # Positive frequencies
    freqs = np.linspace(0, s_freq / 2, NptsF)
    FR = s_freq / NFFT

    # Hamming window and padding
    win = hamming(NFFT + 1)[:-1]  # MATLAB-style periodic Hamming window
    pad_front = round((NFFT - len(data)) / 2)
    pad_back = NFFT - len(data) - pad_front
    data_padded = np.pad(data, (pad_front, pad_back), 'constant')
    data_windowed = win * data_padded

    # FFT and power calculation
    whole_FFT = fft(data_windowed, NFFT)
    myFFT = whole_FFT[:NptsF]
    myPower = np.abs(myFFT / 100) ** 2  # Normalized power
    peak_power = np.max(myPower)
    peak_freq = freqs[np.argmax(myPower)]

    # Use peak-to-peak frequency if available
    if peak_to_peak_freq == 1 and peak_freq1 != 0:
        peak_freq = peak_freq1

    HFO_si = np.min(np.where(freqs > HFO_FREQ))
    HFO_ei = np.max(np.where(freqs < MAX_FREQ))
    total_power_cfa = np.sum(myPower[:HFO_si])  # Power in conventional range
    total_power_hfo = np.sum(myPower[HFO_si:])  # Power in HFO range

    if peak_freq >= HFO_FREQ or total_power_hfo >= pwr_thresh * total_power_cfa:
        flag = 1  # True HFO

    if noise_freq_removal == 1:
        for noise_freq in noise_freq_vector:
            if noise_freq - 2 < peak_freq < noise_freq + 2:
                flag = 2  # Not true HFO (too close to noise frequency)

    # Maximum significant frequency in FFT
    significant_freqs = freqs[myPower > peak_power * max_freq_thresh]
    if significant_freqs.size > 0:
        max_freq = significant_freqs[-1]
    else:
        flag = 0  # No significant frequency found
        max_freq = 0

    # Analyze in chunks if data length exceeds FFT length
    if len(data) > FFT_length:
        flag = 0
        f, t, Sxx = spectrogram(data, s_freq, window='hamming', nperseg=FFT_length, noverlap=FFT_length // 2)
        peak_power_vec = np.max(Sxx, axis=0)
        peak_freq_vec = f[np.argmax(Sxx, axis=0)]
        total_power_cfa_vec = np.sum(Sxx[:HFO_si, :], axis=0)
        total_power_hfo_vec = np.sum(Sxx[HFO_si:, :], axis=0)

        for i in range(len(t)):
            if peak_freq_vec[i] >= HFO_FREQ or total_power_hfo_vec[i] >= pwr_thresh * total_power_cfa_vec[i]:
                flag = 1  # True HFO

        if noise_freq_removal == 1:
            for noise_freq in noise_freq_vector:
                if np.sum((noise_freq - 2 < peak_freq_vec) & (peak_freq_vec < noise_freq + 2)) > len(peak_freq_vec) / 2:
                    flag = 2  # Not true HFO (too close to noise frequency)

        total_power_hfo = np.mean(total_power_hfo_vec)
        peak_power = np.mean(peak_power_vec)
        if peak_to_peak_freq == 0:
            peak_freq = np.mean(peak_freq_vec)
            max_freq = np.mean([f[np.argmax(Sxx[:, i] > peak_power_vec[i] * max_freq_thresh)] for i in range(len(t))])

    return flag, duration, peak_power, total_power_hfo, peak_freq, my_amp, max_freq
