# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

### Originally this was a py app for mac and windows (refer to)
## Always check this when working on the backend code to understand the original code
/Users/<USER>/Work/biormika/biormika-original-backend

## Project Overview

Biormika HFO Detector is a web-based application for detecting High-Frequency Oscillations (HFOs) in EEG data, converted from a PyQt5 desktop application. The system processes EDF (European Data Format) files to identify HFO events that help in epilepsy patient analysis.

HFOs are brief oscillatory events in the EEG signal (typically 80-500 Hz) that are biomarkers for epileptogenic tissue. This application helps clinicians analyze large EEG recordings to automatically detect and characterize these events.

## Architecture

### Backend (FastAPI + Python)
- **Location**: `/backend/`
- **Framework**: FastAPI with WebSocket support for real-time streaming
- **Core Algorithm**: Original HFO detection code preserved in `/backend/core/hfo_engine/`
- **Processing Strategy**: Chunks EDF files into 10-second segments for real-time feedback
- **Python Version**: 3.x (compatible with NumPy, SciPy ecosystem)

#### Key Backend Services:
- **app.py**: Main FastAPI application with endpoints and WebSocket handling
- **services/analysis_service.py**: EEGStreamProcessor for chunked HFO detection
- **models/parameters.py**: Pydantic models for all analysis parameters
- **core/validators/**: Comprehensive validation system
  - `edf_validator.py`: EDF file format and header validation
  - `parameter_validator.py`: Analysis parameter validation
  - `signal_validator.py`: Signal quality checks
- **core/exceptions/**: Custom exception handling
  - `validation_exceptions.py`: Validation-specific exceptions
  - `error_handlers.py`: Global error handling setup

### Frontend (Next.js + React)
- **Location**: `/frontend/`
- **Framework**: Next.js 15.4.5 with React 19.1.0
- **UI Library**: Tailwind CSS v4 with custom components
- **Visualization**: Plotly.js for interactive EEG charts
- **State Management**: React hooks with WebSocket context
- **TypeScript**: Strict mode enabled for type safety

#### Key Frontend Components:
- **FileUploadCard**: EDF file upload with drag-and-drop support
- **ParameterSettingsCard**: Comprehensive parameter configuration UI
- **ParameterSections/**:
  - `ThresholdSection`: HFO detection threshold configuration
  - `FrequencySection`: Frequency band selection
  - `MontageSection`: Montage type selection
  - `TimeSegmentSection`: Time range specification
  - `ChannelSelectionSection`: Channel/lead selection with contact specs
- **EEGViewer/**: Real-time EEG visualization
  - `PlotlyChart`: Interactive waveform display
  - `ChannelGrid`: Multi-channel layout
  - `ChannelRow`: Individual channel controls
- **ConnectionStatus**: WebSocket connection indicator
- **ProgressIndicator**: Analysis progress display

## API Endpoints

### REST Endpoints:
- `GET /api/parameters/options`: Get all available parameter options
- `POST /api/analyze`: Initial file validation and metadata extraction
- `POST /api/analyze/start`: Start analysis with full parameter validation

### WebSocket:
- `/ws`: Real-time streaming endpoint for:
  - Preview data chunks
  - HFO detection results
  - Analysis progress updates
  - Error notifications

## Validation System

The application implements comprehensive validation matching the original desktop version:

### File Validation
- File existence and readability
- .edf extension requirement
- Minimum file size (256 bytes)
- EDF version compatibility

### EDF Header Validation
- Required fields presence
- Channel count > 0
- Duration > 0
- Valid physical/digital ranges
- No zero scaling factors
- Duplicate channel detection

### Sampling Rate Requirements
- Minimum 200 Hz for HFO detection
- Support for variable sampling rates across channels
- Nyquist frequency constraints (high_cutoff <= sampling_rate/2)
- MAX_FREQ = sampling_rate/3 constraint

### Signal Quality Checks
- Flat signal detection (> 100ms constant value)
- NaN/Inf value detection
- Minimum signal variance
- Discontinuity detection
- Minimum signal length (1 second)

## User-Configurable Parameters

### 1. HFO Detection Thresholds
- **Amplitude 1**: 2-5 (default: 2) - HFO amplitude >= energy signal (times std)
- **Amplitude 2**: 2-5 (default: 2) - HFO amplitude >= baseline (times std)
- **Peaks 1**: 2-8 (default: 6) - Number of peaks >= Amplitude 1
- **Peaks 2**: 2-6 (default: 3) - Number of peaks >= Amplitude 2
- **Duration**: 5-15 ms (default: 10) - Minimum HFO length
- **Temporal Sync**: 5-12 ms (default: 10) - Inter-HFO interval in channel
- **Spatial Sync**: 5-12 ms (default: 10) - Inter-HFO interval across channels

### 2. Montage Types
- **Bipolar** (default): Sequential channel subtraction
- **Average**: Common average reference
- **Referential**: Single reference channel (user-specified)

### 3. Frequency Bands
- **Low cutoff options**: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250] Hz
- **High cutoff options**: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660] Hz
- **Default**: 50-300 Hz (ripple band)

### 4. Time Segment Selection
- **Entire file** (default)
- **Start/End times**: Specify exact time range (dd.mm.yy HH:MM:SS format)
- **Start time + Duration**: Specify start and duration in seconds

### 5. Channel Selection
- Dynamic lead group extraction from EDF
- Contact specification syntax:
  - Ranges: "1-5" (contacts 1 through 5)
  - Individual: "1,3,5" (specific contacts)
  - Mixed: "1-3,5,7-9" (combination)

## Processing Workflow

1. **File Upload**: User uploads EDF file via drag-and-drop or file picker
2. **Initial Validation**: Quick file format and header checks
3. **Parameter Configuration**: User configures detection parameters
4. **Analysis Start**: Full validation and WebSocket connection established
5. **Streaming Processing**:
   - EDF data chunked into 10-second segments
   - Each chunk processed through HFO detection algorithm
   - Results streamed back via WebSocket
   - UI updates in real-time
6. **Results Display**: HFO events marked on EEG traces

## Development Setup

### Backend:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app:app --reload --port 8000
```

### Frontend:
```bash
cd frontend
npm install
npm run dev
```

### Testing:
- Backend: `python test_comprehensive_validation.py`
- API: `python test_api.py`

## Important Implementation Notes

1. **WebSocket Communication**: Uses JSON messages with type field for routing
2. **Error Handling**: All errors include detailed messages and validation context
3. **Memory Management**: Streaming approach prevents loading entire EDF into memory
4. **Cross-Origin**: CORS configured for localhost:3000 and localhost:3002
5. **File Path Handling**: Absolute paths required for file operations

## Key Technical Decisions

1. **Chunked Processing**: 10-second chunks balance responsiveness and efficiency
2. **Validation First**: Comprehensive validation prevents runtime errors
3. **Type Safety**: Pydantic models ensure data integrity
4. **Real-time Feedback**: WebSocket provides immediate user feedback
5. **Original Algorithm**: Core HFO detection logic preserved exactly

## Future Considerations

- Result export functionality
- Batch processing capabilities
- Cloud storage integration
- Multi-user support
- Performance optimization for large files

