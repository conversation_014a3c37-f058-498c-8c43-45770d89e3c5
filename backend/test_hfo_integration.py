#!/usr/bin/env python
"""Test script to verify HFO detection integration"""

import sys
import asyncio
import numpy as np

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from services.analysis_service import AnalysisService
from models.parameters import AnalysisParameters

async def test_hfo_detection():
    """Test HFO detection with a sample file"""
    
    # Define test parameters
    test_params = {
        "thresholds": {
            "amplitude1": 2,
            "amplitude2": 2,
            "peaks1": 6,
            "peaks2": 3,
            "duration": 10,
            "temporal_sync": 10,
            "spatial_sync": 10
        },
        "montage": {"type": "bipolar"},
        "frequency": {"low_cutoff": 50, "high_cutoff": 300},
        "time_segment": {"mode": "entire_file"},
        "channel_selection": {"selected_leads": [], "contact_specifications": {}}
    }
    
    # Create parameters object
    parameters = AnalysisParameters(**test_params)
    
    # You'll need to update this path to an actual EDF file
    test_file_path = "/path/to/test.edf"
    
    print("Testing HFO detection integration...")
    print(f"File: {test_file_path}")
    print(f"Parameters: {test_params}")
    
    try:
        # Create analysis service
        service = AnalysisService()
        
        # Create processor
        processor = await service.create_processor(test_file_path, parameters)
        
        print(f"✓ Processor initialized successfully")
        print(f"✓ HFO detection completed")
        print(f"  Total HFOs detected: {processor.total_hfos}")
        
        # Test preview
        preview = await processor.process_preview()
        print(f"✓ Preview processed")
        print(f"  Preview HFO count: {preview.get('quick_hfo_count', 0)}")
        
        # Test chunk processing
        total_chunks = processor.get_total_chunks()
        print(f"✓ Total chunks to process: {total_chunks}")
        
        if total_chunks > 0:
            # Process first chunk
            chunk_result = await processor.process_chunk(0)
            print(f"✓ First chunk processed")
            print(f"  HFOs in chunk: {len(chunk_result.get('hfo_events', []))}")
            
            # Show first HFO if available
            if chunk_result.get('hfo_events'):
                first_hfo = chunk_result['hfo_events'][0]
                print(f"  First HFO details:")
                print(f"    - Channel: {first_hfo.get('channel')}")
                print(f"    - Start time: {first_hfo.get('start_time'):.3f}s")
                print(f"    - Duration: {first_hfo.get('duration_ms', 'N/A')}ms")
                print(f"    - Peak frequency: {first_hfo.get('peak_frequency', 'N/A')}Hz")
        
        # Get summary
        summary = processor.get_summary()
        print(f"✓ Analysis summary:")
        print(f"  - Total HFOs: {summary['total_hfos']}")
        print(f"  - Channels analyzed: {summary['channels_analyzed']}")
        print(f"  - Frequency range: {summary['frequency_range']}")
        print(f"  - Montage: {summary['montage']}")
        
        print("\n✅ HFO detection integration test PASSED!")
        
    except FileNotFoundError:
        print(f"⚠️  Test file not found: {test_file_path}")
        print("   Please update the test_file_path variable with a valid EDF file path")
    except Exception as e:
        print(f"❌ Test FAILED with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_hfo_detection())