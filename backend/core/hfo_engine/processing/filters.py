"""
Filter functions for HFO detection signal processing.
Extracted from hfo_analysis.py for better modularity.
"""

import numpy as np
from scipy.signal import butter, filtfilt
from typing import <PERSON><PERSON>, Union, List


def create_notch_filter(Wo: float, BW: float, Ab: float = -3) -> Tuple[np.ndarray, np.ndarray]:
    """
    Design a second-order IIR notch digital filter.
    
    Args:
        Wo: Normalized notch frequency (Wo = frequency / (sampling_rate / 2))
        BW: Bandwidth of the notch filter
        Ab: Attenuation in dB (default is -3 dB)
        
    Returns:
        Tuple of (numerator coefficients, denominator coefficients)
    """
    Gb = 10 ** (-Ab / 20)  # Convert attenuation from dB to linear gain
    
    # Ensure Gb is valid (0 < Gb <= 1), if not clamp it to avoid invalid square root
    Gb = np.clip(Gb, 0, 1)
    
    BW = BW * np.pi  # Normalize bandwidth
    Wo = Wo * np.pi  # Normalize frequency
    
    # Avoid invalid sqrt if Gb exceeds 1
    beta = (np.sqrt(np.abs(1 - Gb ** 2)) / Gb) * np.tan(BW / 2)  # Calculate beta
    gain = 1 / (1 + beta)  # Gain calculation
    
    # Filter coefficients (numerator and denominator)
    num = gain * np.array([1, -2 * np.cos(Wo), 1])
    den = np.array([1, -2 * gain * np.cos(Wo), 2 * gain - 1])
    
    return num, den


def apply_bandpass_filter(data: np.ndarray, 
                         low_cutoff: float, 
                         high_cutoff: float, 
                         sampling_rate: float,
                         filter_order: int = 4) -> np.ndarray:
    """
    Apply a bandpass Butterworth filter to the data.
    
    Args:
        data: Input signal data
        low_cutoff: Low cutoff frequency in Hz
        high_cutoff: High cutoff frequency in Hz
        sampling_rate: Sampling rate in Hz
        filter_order: Order of the Butterworth filter (default: 4)
        
    Returns:
        Filtered signal
    """
    nyquist = sampling_rate / 2
    low = low_cutoff / nyquist
    high = high_cutoff / nyquist
    
    b, a = butter(filter_order, [low, high], btype='band')
    filtered_data = filtfilt(b, a, data)
    
    return filtered_data


def apply_notch_filter(data: np.ndarray,
                      notch_frequencies: Union[float, List[float]],
                      sampling_rate: float,
                      bandwidth: float = 3.0) -> np.ndarray:
    """
    Apply notch filter(s) to remove specific frequencies (e.g., line noise).
    
    Args:
        data: Input signal data
        notch_frequencies: Single frequency or list of frequencies to notch out
        sampling_rate: Sampling rate in Hz
        bandwidth: Bandwidth of the notch filter in Hz (default: 3.0)
        
    Returns:
        Notch-filtered signal
    """
    if isinstance(notch_frequencies, (int, float)):
        notch_frequencies = [notch_frequencies]
    
    filtered_data = data.copy()
    nyquist = sampling_rate / 2
    
    for freq in notch_frequencies:
        # Normalized frequency
        w0 = freq / nyquist
        # Normalized bandwidth
        bw = bandwidth / nyquist
        
        # Create and apply notch filter
        b, a = create_notch_filter(w0, bw)
        filtered_data = filtfilt(b, a, filtered_data)
    
    return filtered_data


def create_filter_bank(sampling_rate: float,
                      frequency_bands: List[Tuple[float, float]]) -> List[Tuple[np.ndarray, np.ndarray]]:
    """
    Create a bank of bandpass filters for multi-band HFO detection.
    
    Args:
        sampling_rate: Sampling rate in Hz
        frequency_bands: List of (low, high) frequency tuples
        
    Returns:
        List of filter coefficient tuples (b, a)
    """
    filter_bank = []
    nyquist = sampling_rate / 2
    
    for low_freq, high_freq in frequency_bands:
        low = low_freq / nyquist
        high = high_freq / nyquist
        b, a = butter(4, [low, high], btype='band')
        filter_bank.append((b, a))
    
    return filter_bank