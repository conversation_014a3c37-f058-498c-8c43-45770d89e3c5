"""
Signal analysis functions for HFO detection.
Handles energy calculations, Hilbert transform, and signal statistics.
"""

import numpy as np
from scipy.signal import hilbert
from typing import Tuple, Optional, Dict


def calculate_energy_statistics(signal: np.ndarray, 
                              window_length: int = 5) -> Dict[str, np.ndarray]:
    """
    Calculate energy statistics of the signal including RMS and Hilbert envelope.
    
    Args:
        signal: Input EEG signal (channels x samples)
        window_length: Window length for RMS calculation in samples
        
    Returns:
        Dictionary containing:
        - 'rms': Root mean square values
        - 'hilbert_envelope': Hilbert envelope of the signal
        - 'mean_hilbert': Mean of Hilbert envelope per channel
        - 'std_hilbert': Standard deviation of Hilbert envelope per channel
        - 'mean_rectified': Mean of rectified signal per channel
        - 'std_rectified': Standard deviation of rectified signal per channel
    """
    num_channels, num_samples = signal.shape
    
    # Calculate rectified signal statistics
    rectified_signal = np.abs(signal)
    mean_rectified = np.mean(rectified_signal, axis=1)
    std_rectified = np.std(rectified_signal, axis=1)
    
    # Calculate Hilbert envelope
    hilbert_envelope = np.abs(hilbert(signal, axis=1))
    mean_hilbert = np.mean(hilbert_envelope, axis=1)
    std_hilbert = np.std(hilbert_envelope, axis=1)
    
    # Calculate RMS in sliding windows (if needed)
    if window_length > 1:
        rms = calculate_rms(signal, window_length)
    else:
        rms = rectified_signal
    
    return {
        'rms': rms,
        'hilbert_envelope': hilbert_envelope,
        'mean_hilbert': mean_hilbert,
        'std_hilbert': std_hilbert,
        'mean_rectified': mean_rectified,
        'std_rectified': std_rectified
    }


def calculate_rms(signal: np.ndarray, window_length: int) -> np.ndarray:
    """
    Calculate root mean square (RMS) in sliding windows.
    
    Args:
        signal: Input signal (channels x samples)
        window_length: Window length in samples
        
    Returns:
        RMS values (channels x samples-window_length+1)
    """
    num_channels, num_samples = signal.shape
    num_windows = num_samples - window_length + 1
    rms = np.zeros((num_channels, num_windows))
    
    for i in range(num_windows):
        window = signal[:, i:i+window_length]
        rms[:, i] = np.sqrt(np.mean(window**2, axis=1))
    
    return rms


def calculate_hilbert_envelope(signal: np.ndarray) -> np.ndarray:
    """
    Calculate the Hilbert envelope (analytic signal magnitude) of the signal.
    
    Args:
        signal: Input signal (channels x samples)
        
    Returns:
        Hilbert envelope of the signal
    """
    return np.abs(hilbert(signal, axis=1))


def calculate_baseline_stats(signal: np.ndarray, 
                           percentile_low: float = 25,
                           percentile_high: float = 75) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate robust baseline statistics using percentiles.
    
    Args:
        signal: Input signal (channels x samples)
        percentile_low: Lower percentile for robust mean estimation
        percentile_high: Upper percentile for robust mean estimation
        
    Returns:
        Tuple of (robust_mean, robust_std) per channel
    """
    num_channels = signal.shape[0]
    robust_mean = np.zeros(num_channels)
    robust_std = np.zeros(num_channels)
    
    for ch in range(num_channels):
        # Use interquartile range for robust statistics
        q_low = np.percentile(signal[ch], percentile_low)
        q_high = np.percentile(signal[ch], percentile_high)
        
        # Select data within the interquartile range
        mask = (signal[ch] >= q_low) & (signal[ch] <= q_high)
        baseline_data = signal[ch][mask]
        
        if len(baseline_data) > 0:
            robust_mean[ch] = np.mean(baseline_data)
            robust_std[ch] = np.std(baseline_data)
        else:
            # Fallback to regular statistics if no data in range
            robust_mean[ch] = np.mean(signal[ch])
            robust_std[ch] = np.std(signal[ch])
    
    return robust_mean, robust_std


def calculate_signal_power(signal: np.ndarray, 
                         sampling_rate: float,
                         freq_bands: Optional[Dict[str, Tuple[float, float]]] = None) -> Dict[str, np.ndarray]:
    """
    Calculate signal power in specified frequency bands.
    
    Args:
        signal: Input signal (channels x samples)
        sampling_rate: Sampling rate in Hz
        freq_bands: Dictionary of frequency bands {name: (low, high)}
                   If None, calculates total power
        
    Returns:
        Dictionary of power values per band per channel
    """
    from scipy.signal import welch
    
    num_channels = signal.shape[0]
    
    # Default frequency bands if not specified
    if freq_bands is None:
        freq_bands = {'total': (0, sampling_rate / 2)}
    
    power_results = {}
    
    for band_name, (f_low, f_high) in freq_bands.items():
        band_power = np.zeros(num_channels)
        
        for ch in range(num_channels):
            # Calculate power spectral density
            freqs, psd = welch(signal[ch], fs=sampling_rate, nperseg=min(256, signal.shape[1]))
            
            # Find frequency indices
            freq_mask = (freqs >= f_low) & (freqs <= f_high)
            
            # Integrate power in the band
            band_power[ch] = np.trapz(psd[freq_mask], freqs[freq_mask])
        
        power_results[band_name] = band_power
    
    return power_results