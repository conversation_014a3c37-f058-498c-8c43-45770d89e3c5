"""
HFO detection functions.
Core detection algorithms extracted from hfo_analysis.py.
"""

import numpy as np
from scipy.signal import find_peaks
from typing import List, Tu<PERSON>, Dict, Optional
from dataclasses import dataclass


@dataclass
class HFOCandidate:
    """Represents a candidate HFO event"""
    start_idx: int
    end_idx: int
    channel: int
    amplitude: float
    num_peaks: int
    duration_ms: float
    peak_indices: Optional[List[int]] = None


def detect_hfo_segments(energy_signal: np.ndarray,
                       threshold: float,
                       min_duration_samples: int,
                       max_gap_samples: int = 3) -> List[Tuple[int, int]]:
    """
    Detect segments where the energy signal exceeds threshold.
    
    Args:
        energy_signal: 1D energy signal (e.g., Hilbert envelope)
        threshold: Detection threshold value
        min_duration_samples: Minimum duration in samples for valid HFO
        max_gap_samples: Maximum gap in samples to merge segments
        
    Returns:
        List of (start, end) indices for detected segments
    """
    # Find samples above threshold
    above_threshold = energy_signal > threshold
    
    # Find start and end of segments
    segments = []
    in_segment = False
    start_idx = 0
    
    for i in range(len(above_threshold)):
        if above_threshold[i] and not in_segment:
            # Start of new segment
            start_idx = i
            in_segment = True
        elif not above_threshold[i] and in_segment:
            # End of segment
            segments.append((start_idx, i))
            in_segment = False
    
    # Handle case where segment extends to end
    if in_segment:
        segments.append((start_idx, len(above_threshold)))
    
    # Merge nearby segments
    merged_segments = merge_nearby_segments(segments, max_gap_samples)
    
    # Filter by minimum duration
    valid_segments = [
        (start, end) for start, end in merged_segments
        if (end - start) >= min_duration_samples
    ]
    
    return valid_segments


def merge_nearby_segments(segments: List[Tuple[int, int]], 
                         max_gap: int) -> List[Tuple[int, int]]:
    """
    Merge segments that are close to each other.
    
    Args:
        segments: List of (start, end) tuples
        max_gap: Maximum gap between segments to merge
        
    Returns:
        Merged segments
    """
    if not segments:
        return []
    
    merged = [segments[0]]
    
    for start, end in segments[1:]:
        last_start, last_end = merged[-1]
        
        if start - last_end <= max_gap:
            # Merge with previous segment
            merged[-1] = (last_start, end)
        else:
            # Add as new segment
            merged.append((start, end))
    
    return merged


def find_hfo_peaks(signal: np.ndarray,
                  segment_start: int,
                  segment_end: int,
                  amplitude_threshold: float,
                  min_peak_distance: int = 3) -> Tuple[np.ndarray, int]:
    """
    Find peaks within an HFO segment.
    
    Args:
        signal: Raw signal data
        segment_start: Start index of segment
        segment_end: End index of segment
        amplitude_threshold: Minimum peak amplitude
        min_peak_distance: Minimum distance between peaks in samples
        
    Returns:
        Tuple of (peak indices, number of peaks above threshold)
    """
    segment = signal[segment_start:segment_end]
    
    # Find all peaks
    peaks, properties = find_peaks(segment, 
                                  height=amplitude_threshold,
                                  distance=min_peak_distance)
    
    # Convert to absolute indices
    if len(peaks) > 0:
        peaks += segment_start
    
    return peaks, len(peaks)


def validate_hfo_candidate(candidate: HFOCandidate,
                         min_peaks: int,
                         min_duration_ms: float,
                         max_duration_ms: float = 100.0) -> bool:
    """
    Validate if a candidate segment is a true HFO.
    
    Args:
        candidate: HFO candidate to validate
        min_peaks: Minimum number of peaks required
        min_duration_ms: Minimum duration in milliseconds
        max_duration_ms: Maximum duration in milliseconds
        
    Returns:
        True if valid HFO, False otherwise
    """
    # Check duration
    if candidate.duration_ms < min_duration_ms or candidate.duration_ms > max_duration_ms:
        return False
    
    # Check number of peaks
    if candidate.num_peaks < min_peaks:
        return False
    
    return True


def detect_hfos_multichannel(signals: np.ndarray,
                           energy_signals: np.ndarray,
                           thresholds: np.ndarray,
                           sampling_rate: float,
                           detection_params: Dict) -> Dict[int, List[HFOCandidate]]:
    """
    Detect HFOs across multiple channels.
    
    Args:
        signals: Raw signals (channels x samples)
        energy_signals: Energy signals (channels x samples)
        thresholds: Detection thresholds per channel
        sampling_rate: Sampling rate in Hz
        detection_params: Dictionary with detection parameters:
            - min_duration_ms: Minimum HFO duration
            - min_peaks: Minimum number of peaks
            - amplitude_threshold_factor: Factor for amplitude threshold
            
    Returns:
        Dictionary mapping channel index to list of HFO candidates
    """
    num_channels = signals.shape[0]
    hfo_detections = {}
    
    # Convert parameters to samples
    min_duration_samples = int(detection_params['min_duration_ms'] * sampling_rate / 1000)
    
    for ch in range(num_channels):
        channel_hfos = []
        
        # Detect segments
        segments = detect_hfo_segments(
            energy_signals[ch],
            thresholds[ch],
            min_duration_samples
        )
        
        # Analyze each segment
        for start_idx, end_idx in segments:
            # Calculate duration
            duration_ms = (end_idx - start_idx) * 1000 / sampling_rate
            
            # Find peaks in raw signal
            baseline = np.mean(signals[ch])
            std_baseline = np.std(signals[ch])
            peak_threshold = baseline + detection_params['amplitude_threshold_factor'] * std_baseline
            
            peaks, num_peaks = find_hfo_peaks(
                signals[ch],
                start_idx,
                end_idx,
                peak_threshold
            )
            
            # Create candidate
            candidate = HFOCandidate(
                start_idx=start_idx,
                end_idx=end_idx,
                channel=ch,
                amplitude=np.max(signals[ch, start_idx:end_idx]) - baseline,
                num_peaks=num_peaks,
                duration_ms=duration_ms,
                peak_indices=peaks.tolist() if len(peaks) > 0 else []
            )
            
            # Validate candidate
            if validate_hfo_candidate(
                candidate,
                detection_params['min_peaks'],
                detection_params['min_duration_ms']
            ):
                channel_hfos.append(candidate)
        
        if channel_hfos:
            hfo_detections[ch] = channel_hfos
    
    return hfo_detections


def check_spatial_coincidence(hfo_detections: Dict[int, List[HFOCandidate]],
                            max_delay_ms: float,
                            sampling_rate: float) -> List[List[HFOCandidate]]:
    """
    Check for spatial coincidence of HFOs across channels.
    
    Args:
        hfo_detections: Dictionary of channel -> HFO candidates
        max_delay_ms: Maximum delay in milliseconds for coincidence
        sampling_rate: Sampling rate in Hz
        
    Returns:
        List of coincident HFO groups
    """
    max_delay_samples = int(max_delay_ms * sampling_rate / 1000)
    coincident_groups = []
    
    # Flatten all HFOs with channel info
    all_hfos = []
    for channel, hfos in hfo_detections.items():
        all_hfos.extend(hfos)
    
    # Sort by start time
    all_hfos.sort(key=lambda x: x.start_idx)
    
    # Find coincident groups
    used = set()
    for i, hfo1 in enumerate(all_hfos):
        if i in used:
            continue
            
        group = [hfo1]
        used.add(i)
        
        for j, hfo2 in enumerate(all_hfos[i+1:], i+1):
            if j in used:
                continue
                
            # Check if within time window
            if abs(hfo1.start_idx - hfo2.start_idx) <= max_delay_samples:
                group.append(hfo2)
                used.add(j)
            elif hfo2.start_idx > hfo1.start_idx + max_delay_samples:
                break
        
        if len(group) > 1:
            coincident_groups.append(group)
    
    return coincident_groups