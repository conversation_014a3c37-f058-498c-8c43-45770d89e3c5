"""Validator for HFO detection threshold parameters"""

from typing import Dict, Any, List, Tuple
from ..base_validator import BaseValida<PERSON>
from config.parameters import ParameterConfig


class ThresholdValidator(BaseValidator):
    """Validates HFO detection threshold parameters"""
    
    def validate(self, thresholds: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate HFO detection threshold parameters
        
        Args:
            thresholds: Dictionary of threshold parameters
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        for param_name, constraints in ParameterConfig.THRESHOLD_CONSTRAINTS.items():
            if param_name not in thresholds:
                self.add_error(f"Missing required threshold parameter: {param_name}")
                continue
            
            value = thresholds[param_name]
            
            # Check type
            if not isinstance(value, (int, float)):
                self.add_error(f"Invalid type for {param_name}: expected number, got {type(value).__name__}")
                continue
            
            # Check range
            if not self.is_in_range(value, constraints['min'], constraints['max']):
                self.add_error(
                    f"Parameter '{param_name}' value {value} out of range [{constraints['min']}, {constraints['max']}]. "
                    f"Description: {constraints['description']}"
                )
        
        # Additional logical constraints
        if 'peaks1' in thresholds and 'peaks2' in thresholds:
            if thresholds['peaks2'] > thresholds['peaks1']:
                self.add_warning(
                    f"peaks2 ({thresholds['peaks2']}) is greater than peaks1 ({thresholds['peaks1']}). "
                    "This is unusual as peaks2 should typically be less restrictive."
                )
        
        # Check temporal and spatial sync relationship
        if 'temporal_sync' in thresholds and 'spatial_sync' in thresholds:
            if thresholds['spatial_sync'] < thresholds['temporal_sync']:
                self.add_warning(
                    f"spatial_sync ({thresholds['spatial_sync']}ms) is less than temporal_sync "
                    f"({thresholds['temporal_sync']}ms). Spatial sync is typically >= temporal sync."
                )
        
        return not self.has_errors(), self.errors
    
    def validate_single_parameter(self, param_name: str, value: float) -> Tuple[bool, str]:
        """
        Validate a single threshold parameter
        
        Args:
            param_name: Name of the parameter
            value: Value to validate
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if param_name not in ParameterConfig.THRESHOLD_CONSTRAINTS:
            return False, f"Unknown threshold parameter: {param_name}"
        
        constraints = ParameterConfig.THRESHOLD_CONSTRAINTS[param_name]
        
        if not isinstance(value, (int, float)):
            return False, f"Invalid type: expected number, got {type(value).__name__}"
        
        if not constraints['min'] <= value <= constraints['max']:
            return False, f"Value {value} out of range [{constraints['min']}, {constraints['max']}]"
        
        return True, ""
    
    def get_default_values(self) -> Dict[str, float]:
        """Get default threshold values"""
        return ParameterConfig.get_threshold_defaults()
    
    def get_parameter_info(self, param_name: str) -> Dict[str, Any]:
        """Get information about a specific parameter"""
        if param_name not in ParameterConfig.THRESHOLD_CONSTRAINTS:
            return {}
        return ParameterConfig.THRESHOLD_CONSTRAINTS[param_name]