"""Validator for frequency filter parameters"""

from typing import Dict, <PERSON>, <PERSON>, Tuple, Optional
from ..base_validator import BaseValida<PERSON>
from config.parameters import ParameterConfig


class FrequencyValidator(BaseValidator):
    """Validates frequency filter parameters"""
    
    def validate(self, frequency: Dict[str, Any], 
                sampling_rate: Optional[float] = None) -> <PERSON><PERSON>[bool, List[str]]:
        """
        Validate frequency filter parameters
        
        Args:
            frequency: Dictionary with low_cutoff and high_cutoff
            sampling_rate: Sampling rate of the EDF file (if available)
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        # Check required fields
        if 'low_cutoff' not in frequency:
            self.add_error("Missing required field: low_cutoff")
        if 'high_cutoff' not in frequency:
            self.add_error("Missing required field: high_cutoff")
        
        if self.has_errors():
            return False, self.errors
        
        low_cutoff = frequency['low_cutoff']
        high_cutoff = frequency['high_cutoff']
        
        # Check types
        if not isinstance(low_cutoff, (int, float)):
            self.add_error(f"Invalid type for low_cutoff: expected number, got {type(low_cutoff).__name__}")
        if not isinstance(high_cutoff, (int, float)):
            self.add_error(f"Invalid type for high_cutoff: expected number, got {type(high_cutoff).__name__}")
        
        if self.has_errors():
            return False, self.errors
        
        # Validate against allowed options
        if not ParameterConfig.validate_frequency_value('low_cutoff', low_cutoff):
            self.add_error(
                f"Invalid low cutoff frequency: {low_cutoff}Hz. "
                f"Valid options: {ParameterConfig.FREQUENCY_CONFIG['low_cutoff_options']}"
            )
        
        if not ParameterConfig.validate_frequency_value('high_cutoff', high_cutoff):
            self.add_error(
                f"Invalid high cutoff frequency: {high_cutoff}Hz. "
                f"Valid options: {ParameterConfig.FREQUENCY_CONFIG['high_cutoff_options']}"
            )
        
        # Check logical constraints
        if low_cutoff >= high_cutoff:
            self.add_error(f"Low cutoff ({low_cutoff}Hz) must be less than high cutoff ({high_cutoff}Hz)")
        
        # Validate against sampling rate if provided
        if sampling_rate:
            self._validate_against_sampling_rate(low_cutoff, high_cutoff, sampling_rate)
        
        return not self.has_errors(), self.errors
    
    def _validate_against_sampling_rate(self, low_cutoff: float, high_cutoff: float, 
                                      sampling_rate: float) -> None:
        """Validate frequency values against sampling rate constraints"""
        nyquist_freq = sampling_rate / 2
        max_usable_freq = sampling_rate / 3
        
        if high_cutoff > nyquist_freq:
            self.add_error(
                f"High cutoff frequency ({high_cutoff}Hz) exceeds Nyquist frequency ({nyquist_freq:.1f}Hz)"
            )
        
        if high_cutoff > max_usable_freq:
            self.add_warning(
                f"High cutoff frequency ({high_cutoff}Hz) exceeds recommended maximum "
                f"({max_usable_freq:.1f}Hz) for reliable detection"
            )
        
        # Check if low cutoff is too low for HFO detection
        if low_cutoff < 30:
            self.add_warning(
                f"Low cutoff frequency ({low_cutoff}Hz) is below typical HFO range (80-500Hz). "
                "This may include non-HFO oscillations."
            )
    
    def get_default_values(self) -> Dict[str, int]:
        """Get default frequency filter values"""
        return ParameterConfig.get_frequency_defaults()
    
    def get_valid_options(self) -> Dict[str, List[int]]:
        """Get valid frequency options"""
        return {
            'low_cutoff': ParameterConfig.FREQUENCY_CONFIG['low_cutoff_options'],
            'high_cutoff': ParameterConfig.FREQUENCY_CONFIG['high_cutoff_options']
        }
    
    def suggest_nearest_valid_frequency(self, freq_type: str, value: float) -> int:
        """Suggest the nearest valid frequency option"""
        options = self.get_valid_options().get(freq_type, [])
        if not options:
            return int(value)
        
        return min(options, key=lambda x: abs(x - value))