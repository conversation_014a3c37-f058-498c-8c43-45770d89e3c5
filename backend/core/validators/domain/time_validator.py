"""Validator for time segment parameters"""

from typing import Dict, <PERSON>, <PERSON>, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta
from ..base_validator import BaseValidator
from config.parameters import ParameterConfig


class TimeValidator(BaseValidator):
    """Validates time segment parameters"""
    
    def validate(self, time_segment: Dict[str, Any], 
                file_duration: Optional[float] = None,
                file_start_time: Optional[datetime] = None) -> Tuple[bool, List[str]]:
        """
        Validate time segment parameters
        
        Args:
            time_segment: Dictionary with mode and time parameters
            file_duration: Total duration of the file in seconds (if available)
            file_start_time: Start time of the file (if available)
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        if 'mode' not in time_segment:
            self.add_error("Missing required field: mode")
            return False, self.errors
        
        mode = time_segment['mode']
        valid_modes = ParameterConfig.TIME_SEGMENT_CONFIG['modes']
        
        if mode not in valid_modes:
            self.add_error(f"Invalid time segment mode: {mode}. Valid modes: {valid_modes}")
            return False, self.errors
        
        # Validate based on mode
        if mode == 'entire_file':
            # No additional validation needed
            pass
        elif mode == 'start_end_times':
            self._validate_start_end_times(time_segment, file_start_time, file_duration)
        elif mode == 'start_time_duration':
            self._validate_start_time_duration(time_segment, file_start_time, file_duration)
        
        return not self.has_errors(), self.errors
    
    def _validate_start_end_times(self, time_segment: Dict[str, Any], 
                                 file_start_time: Optional[datetime],
                                 file_duration: Optional[float]) -> None:
        """Validate start/end times mode"""
        if 'start_time' not in time_segment:
            self.add_error("Missing required field for start_end_times mode: start_time")
        if 'end_time' not in time_segment:
            self.add_error("Missing required field for start_end_times mode: end_time")
        
        if self.has_errors():
            return
        
        # Parse times
        try:
            start_time = self._parse_time(time_segment['start_time'])
            end_time = self._parse_time(time_segment['end_time'])
        except ValueError as e:
            self.add_error(f"Invalid time format: {str(e)}")
            return
        
        # Check logical constraints
        if start_time >= end_time:
            self.add_error("Start time must be before end time")
        
        # Validate against file times if available
        if file_start_time and file_duration:
            file_end_time = file_start_time + timedelta(seconds=file_duration)
            
            if start_time < file_start_time:
                self.add_error(f"Start time ({start_time}) is before file start ({file_start_time})")
            if end_time > file_end_time:
                self.add_error(f"End time ({end_time}) is after file end ({file_end_time})")
    
    def _validate_start_time_duration(self, time_segment: Dict[str, Any],
                                    file_start_time: Optional[datetime],
                                    file_duration: Optional[float]) -> None:
        """Validate start time + duration mode"""
        if 'start_time' not in time_segment:
            self.add_error("Missing required field for start_time_duration mode: start_time")
        if 'duration' not in time_segment:
            self.add_error("Missing required field for start_time_duration mode: duration")
        
        if self.has_errors():
            return
        
        # Parse start time
        try:
            start_time = self._parse_time(time_segment['start_time'])
        except ValueError as e:
            self.add_error(f"Invalid time format: {str(e)}")
            return
        
        # Validate duration
        duration = time_segment['duration']
        if not isinstance(duration, (int, float)):
            self.add_error(f"Invalid duration type: expected number, got {type(duration).__name__}")
            return
        
        if duration <= 0:
            self.add_error("Duration must be positive")
        
        # Validate against file times if available
        if file_start_time and file_duration:
            if start_time < file_start_time:
                self.add_error(f"Start time ({start_time}) is before file start ({file_start_time})")
            
            segment_end = start_time + timedelta(seconds=duration)
            file_end_time = file_start_time + timedelta(seconds=file_duration)
            
            if segment_end > file_end_time:
                self.add_error(f"Segment end ({segment_end}) exceeds file end ({file_end_time})")
    
    def _parse_time(self, time_str: str) -> datetime:
        """Parse time string in the expected format"""
        date_format = ParameterConfig.TIME_SEGMENT_CONFIG['date_format']
        return datetime.strptime(time_str, date_format)
    
    def calculate_analysis_times(self, time_segment: Dict[str, Any],
                               file_start_time: datetime,
                               file_duration: float) -> Tuple[float, float]:
        """
        Calculate actual analysis start and end times in seconds from file start
        
        Args:
            time_segment: Validated time segment parameters
            file_start_time: Start time of the file
            file_duration: Total duration in seconds
            
        Returns:
            Tuple[float, float]: (start_seconds, end_seconds) from file start
        """
        mode = time_segment['mode']
        
        if mode == 'entire_file':
            return 0.0, file_duration
        
        elif mode == 'start_end_times':
            start_time = self._parse_time(time_segment['start_time'])
            end_time = self._parse_time(time_segment['end_time'])
            
            start_seconds = (start_time - file_start_time).total_seconds()
            end_seconds = (end_time - file_start_time).total_seconds()
            
            return max(0, start_seconds), min(file_duration, end_seconds)
        
        elif mode == 'start_time_duration':
            start_time = self._parse_time(time_segment['start_time'])
            duration = time_segment['duration']
            
            start_seconds = (start_time - file_start_time).total_seconds()
            end_seconds = start_seconds + duration
            
            return max(0, start_seconds), min(file_duration, end_seconds)
        
        return 0.0, file_duration