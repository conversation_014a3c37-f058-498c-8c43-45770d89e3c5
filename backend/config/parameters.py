"""
Single source of truth for all parameter definitions and constraints.
This module centralizes all parameter-related constants and configurations
to eliminate duplication across the codebase.
"""

from typing import Dict, List, Any, ClassVar


class ParameterConfig:
    """Central configuration for all HFO detection parameters"""
    
    # Threshold parameter constraints
    THRESHOLD_CONSTRAINTS = {
        'amplitude1': {
            'min': 2, 
            'max': 5, 
            'default': 2,
            'step': 1,
            'description': 'HFO amp >= energy signal by (times of std)'
        },
        'amplitude2': {
            'min': 2, 
            'max': 5, 
            'default': 2,
            'step': 1,
            'description': 'HFO amp >= mean baseline signal by (times of std)'
        },
        'peaks1': {
            'min': 2, 
            'max': 8, 
            'default': 6,
            'step': 1,
            'description': 'Number of peaks in HFO >= Amplitude 1'
        },
        'peaks2': {
            'min': 2, 
            'max': 6, 
            'default': 3,
            'step': 1,
            'description': 'Number of peaks in HFO >= Amplitude 2'
        },
        'duration': {
            'min': 5, 
            'max': 15, 
            'default': 10,
            'step': 1,
            'description': 'HFO length >= (ms)'
        },
        'temporal_sync': {
            'min': 5, 
            'max': 12, 
            'default': 10,
            'step': 1,
            'description': 'Inter HFO interval in any channel <= (ms)'
        },
        'spatial_sync': {
            'min': 5, 
            'max': 12, 
            'default': 10,
            'step': 1,
            'description': 'Inter HFO interval across channels <= (ms)'
        }
    }
    
    # Montage configuration
    MONTAGE_CONFIG = {
        'types': ['bipolar', 'average', 'referential'],
        'default': 'bipolar'
    }
    
    # Frequency filter options
    FREQUENCY_CONFIG = {
        'low_cutoff_options': [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],
        'high_cutoff_options': [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],
        'default_low': 50,
        'default_high': 300
    }
    
    # Time segment configuration
    TIME_SEGMENT_CONFIG = {
        'modes': ['entire_file', 'start_end_times', 'start_time_duration'],
        'default': 'entire_file',
        'date_format': '%d.%m.%y %H:%M:%S'
    }
    
    # Signal processing constants
    SIGNAL_CONSTANTS = {
        'min_sampling_rate': 200,  # Hz
        'min_signal_duration': 1.0,  # seconds
        'min_variance': 1e-10,
        'flat_signal_duration': 0.1,  # seconds (100ms)
        'discontinuity_threshold': 100  # samples
    }
    
    # HFO detection constants
    HFO_CONSTANTS = {
        'window_length': 5,  # ms for RMS calculation
        'min_break': 10,  # ms separation between HFOs
        'power_threshold': 1,
        'chunk_duration': 10,  # seconds for streaming
        'overlap_duration': 0.5,  # seconds overlap between chunks
        'threshold_scheme': 10  # Default threshold scheme
    }
    
    @classmethod
    def get_parameter_options(cls) -> Dict[str, Any]:
        """Get all parameter options for UI/API consumption"""
        return {
            'thresholds': cls.THRESHOLD_CONSTRAINTS,
            'montage': cls.MONTAGE_CONFIG,
            'frequency': cls.FREQUENCY_CONFIG,
            'time_segment': cls.TIME_SEGMENT_CONFIG
        }
    
    @classmethod
    def get_threshold_defaults(cls) -> Dict[str, float]:
        """Get default values for threshold parameters"""
        return {
            param: config['default'] 
            for param, config in cls.THRESHOLD_CONSTRAINTS.items()
        }
    
    @classmethod
    def get_frequency_defaults(cls) -> Dict[str, int]:
        """Get default frequency filter values"""
        return {
            'low_cutoff': cls.FREQUENCY_CONFIG['default_low'],
            'high_cutoff': cls.FREQUENCY_CONFIG['default_high']
        }
    
    @classmethod
    def validate_threshold_value(cls, param_name: str, value: float) -> bool:
        """Validate a single threshold parameter value"""
        if param_name not in cls.THRESHOLD_CONSTRAINTS:
            return False
        
        constraints = cls.THRESHOLD_CONSTRAINTS[param_name]
        return constraints['min'] <= value <= constraints['max']
    
    @classmethod
    def validate_frequency_value(cls, cutoff_type: str, value: int) -> bool:
        """Validate a frequency cutoff value"""
        if cutoff_type == 'low_cutoff':
            return value in cls.FREQUENCY_CONFIG['low_cutoff_options']
        elif cutoff_type == 'high_cutoff':
            return value in cls.FREQUENCY_CONFIG['high_cutoff_options']
        return False