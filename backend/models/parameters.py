from pydantic import BaseModel, Field, validator
from typing import List, Optional, Literal, Dict, Any, Union
from datetime import datetime
from config.parameters import ParameterConfig

class ThresholdParameters(BaseModel):
    """HFO Detection threshold parameters matching original UI"""
    amplitude1: int = Field(default=2, ge=2, le=5, description="HFO amp >= energy signal by (times of std)")
    amplitude2: int = Field(default=2, ge=2, le=5, description="HFO amp >= mean baseline signal by (times of std)")
    peaks1: int = Field(default=6, ge=2, le=8, description="Number of peaks in HFO >= Amplitude 1")
    peaks2: int = Field(default=3, ge=2, le=6, description="Number of peaks in HFO >= Amplitude 2")
    duration: int = Field(default=10, ge=5, le=15, description="HFO length >= (ms)")
    temporal_sync: int = Field(default=10, ge=5, le=12, description="Inter HFO interval in any channel <= (ms)")
    spatial_sync: int = Field(default=10, ge=5, le=12, description="Inter HFO interval across channels <= (ms)")

class MontageConfig(BaseModel):
    """Montage configuration for EEG analysis"""
    type: Literal["bipolar", "average", "referential"] = "bipolar"
    reference_channel: Optional[str] = None  # Single reference channel for referential montage
    
    @validator('reference_channel')
    def validate_reference(cls, v, values):
        if values.get('type') == 'referential' and not v:
            raise ValueError("Reference channel required for referential montage")
        return v

class FrequencyFilter(BaseModel):
    """Frequency band selection for HFO detection"""
    low_cutoff: int = Field(
        default=ParameterConfig.FREQUENCY_CONFIG['default_low'], 
        description="Low cutoff filter (Hz)"
    )
    high_cutoff: int = Field(
        default=ParameterConfig.FREQUENCY_CONFIG['default_high'], 
        description="High cutoff filter (Hz)"
    )
    
    @validator('low_cutoff')
    def validate_low_cutoff(cls, v):
        low_options = ParameterConfig.FREQUENCY_CONFIG['low_cutoff_options']
        if v not in low_options:
            # Find nearest valid option
            nearest = min(low_options, key=lambda x: abs(x - v))
            raise ValueError(f"Low cutoff {v} not in valid options. Nearest valid option: {nearest}")
        return v
    
    @validator('high_cutoff')
    def validate_high_cutoff(cls, v, values):
        high_options = ParameterConfig.FREQUENCY_CONFIG['high_cutoff_options']
        if v not in high_options:
            # Find nearest valid option
            nearest = min(high_options, key=lambda x: abs(x - v))
            raise ValueError(f"High cutoff {v} not in valid options. Nearest valid option: {nearest}")
        # Check that high > low
        if 'low_cutoff' in values and v <= values['low_cutoff']:
            raise ValueError(f"High cutoff ({v}) must be greater than low cutoff ({values['low_cutoff']})")
        return v

class TimeSegment(BaseModel):
    """Time segment specification for analysis"""
    mode: Literal["entire_file", "start_end_times", "start_time_duration"] = "entire_file"
    start_date: Optional[str] = None  # Format: "dd.mm.yy"
    start_time: Optional[str] = None  # Format: "HH:MM:SS"
    end_date: Optional[str] = None    # Format: "dd.mm.yy"
    end_time: Optional[str] = None    # Format: "HH:MM:SS"
    duration_seconds: Optional[float] = None
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v:
            # Check format dd.mm.yy
            import re
            if not re.match(r'^\d{2}\.\d{2}\.\d{2}$', v):
                raise ValueError(f"Date must be in format dd.mm.yy, got: {v}")
        return v
    
    @validator('start_time', 'end_time')
    def validate_time_format(cls, v):
        if v:
            # Check format HH:MM:SS or HH.MM.SS
            import re
            v = v.replace('.', ':')  # Allow both . and : as separators
            if not re.match(r'^\d{2}:\d{2}:\d{2}$', v):
                raise ValueError(f"Time must be in format HH:MM:SS, got: {v}")
        return v

class ChannelSelection(BaseModel):
    """Channel/lead selection configuration"""
    selected_leads: List[str] = []  # Lead names that are selected
    contact_specifications: Dict[str, str] = {}  # Lead name -> contact specification (e.g., "1-5,7,9")
    
    def parse_contact_specification(self, spec: str) -> List[int]:
        """Parse contact specification like '1-5,7,9' into list of indices"""
        if not spec:
            return []
        
        contacts = []
        parts = spec.split(',')
        for part in parts:
            part = part.strip()
            if '-' in part:
                # Range specification
                start, end = part.split('-')
                contacts.extend(range(int(start), int(end) + 1))
            else:
                # Single contact
                contacts.append(int(part))
        return sorted(set(contacts))  # Remove duplicates and sort

class AnalysisParameters(BaseModel):
    """Complete analysis parameters for HFO detection"""
    thresholds: ThresholdParameters = ThresholdParameters()
    montage: MontageConfig = MontageConfig()
    frequency: FrequencyFilter = FrequencyFilter()
    time_segment: TimeSegment = TimeSegment()
    channel_selection: ChannelSelection = ChannelSelection()
